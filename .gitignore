# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
.fvm/
.fvmrc
.trae/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
/android/app/.cxx
*.lock
*.sqlite
.qodo
.augment/rules/button-rules.md
.augment/rules/dialog-patterns.md
.augment/rules/file-organization-rules.md
.augment/rules/flavor-architecture-rules.md
.augment/rules/gp-rules.md
.augment/rules/migration-rules.md
.augment/rules/model-rules.md
.augment/rules/network-rules.md
.augment/rules/sizing-rules.md
.augment/rules/state-management-rules.md
.augment/rules/theme-rules.md
.augment/rules/translation-rules.md
