import 'dart:async' show StreamSink;

import 'package:flutter/material.dart';
import 'package:flutter_chen_kchart/utils/number_util.dart';

import '../entity/info_window_entity.dart';
import '../entity/k_line_entity.dart';
import '../utils/date_format_util.dart';
import '../utils/drawing_tool_manager.dart';
import 'base_chart_painter.dart';
import 'base_chart_renderer.dart';
import 'main_renderer.dart';
import 'secondary_renderer.dart';
import 'vol_renderer.dart';

class TrendLine {
  final Offset p1;
  final Offset p2;
  final double maxHeight;
  final double scale;

  TrendLine(this.p1, this.p2, this.maxHeight, this.scale);
}

double? trendLineX;

double getTrendLineX() {
  return trendLineX ?? 0;
}

class ChartPainter extends BaseChartPainter {
  final List<TrendLine> lines; //For TrendLine (保留兼容性)
  final bool isTrendLine; //For TrendLine (保留兼容性)
  bool isrecordingCord = false; //For TrendLine (保留兼容性)
  final double selectY; //For TrendLine (保留兼容性)

  // 新增绘图工具支持
  final DrawingToolManager? drawingToolManager;

  static double get maxScrollX => BaseChartPainter.maxScrollX;
  late BaseChartRenderer mMainRenderer;
  BaseChartRenderer? mVolRenderer, mSecondaryRenderer;
  StreamSink<InfoWindowEntity?>? sink;
  Color? upColor, dnColor;
  Color? ma5Color, ma10Color, ma30Color;
  Color? volColor;
  Color? macdColor, difColor, deaColor, jColor;
  int fixedLength;
  List<int> maDayList;
  final ChartColors chartColors;
  late Paint selectPointPaint, selectorBorderPaint, nowPricePaint;
  late Paint crosshairLabelBackgroundPaint, crosshairLabelBorderPaint;
  @override
  final ChartStyle chartStyle;
  final bool hideGrid;
  final bool showNowPrice;
  final VerticalTextAlignment verticalTextAlignment;

  ChartPainter(
    this.chartStyle,
    this.chartColors, {
    required this.lines, //For TrendLine (保留兼容性)
    required this.isTrendLine, //For TrendLine (保留兼容性)
    required this.selectY, //For TrendLine (保留兼容性)
    this.drawingToolManager, // 新增绘图工具管理器
    required datas,
    required scaleX,
    required scrollX,
    required isLongPass,
    required selectX,
    required xFrontPadding,
    isOnTap,
    isTapShowInfoDialog,
    required this.verticalTextAlignment,
    mainState,
    volHidden,
    secondaryState,
    this.sink,
    bool isLine = false,
    this.hideGrid = false,
    this.showNowPrice = true,
    this.fixedLength = 2,
    this.maDayList = const [5, 10, 20],
  }) : super(chartStyle,
            datas: datas,
            scaleX: scaleX,
            scrollX: scrollX,
            isLongPress: isLongPass,
            isOnTap: isOnTap,
            isTapShowInfoDialog: isTapShowInfoDialog,
            selectX: selectX,
            mainState: mainState,
            volHidden: volHidden,
            secondaryState: secondaryState,
            xFrontPadding: xFrontPadding,
            isLine: isLine) {
    selectPointPaint = Paint()
      ..isAntiAlias = true
      ..strokeWidth = 0.5
      ..color = chartColors.selectFillColor;
    selectorBorderPaint = Paint()
      ..isAntiAlias = true
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke
      ..color = chartColors.selectBorderColor;

    crosshairLabelBackgroundPaint = Paint()
      ..isAntiAlias = true
      ..strokeWidth = 0.5
      ..color = chartColors.crosshairLabelBackgroundColor;
    crosshairLabelBorderPaint = Paint()
      ..isAntiAlias = true
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke
      ..color = chartColors.crosshairLabelBorderColor;

    nowPricePaint = Paint()
      ..strokeWidth = chartStyle.nowPriceLineWidth
      ..isAntiAlias = true;
  }

  @override
  void initChartRenderer() {
    if (datas != null && datas!.isNotEmpty) {
      var t = datas![0];
      fixedLength = NumberUtil.getMaxDecimalLength(t.open, t.close, t.high, t.low);
    }
    mMainRenderer = MainRenderer(
      mMainRect,
      mMainMaxValue,
      mMainMinValue,
      mTopPadding,
      mainState,
      isLine,
      fixedLength,
      chartStyle,
      chartColors,
      scaleX,
      verticalTextAlignment,
      maDayList,
    );
    if (mVolRect != null) {
      mVolRenderer =
          VolRenderer(mVolRect!, mVolMaxValue, mVolMinValue, mChildPadding, fixedLength, chartStyle, chartColors);
    }
    if (mSecondaryRect != null) {
      mSecondaryRenderer = SecondaryRenderer(mSecondaryRect!, mSecondaryMaxValue, mSecondaryMinValue, mChildPadding,
          secondaryState, fixedLength, chartStyle, chartColors);
    }
  }

  @override
  void drawBg(Canvas canvas, Size size) {
    Paint mBgPaint = Paint();
    Gradient mBgGradient = LinearGradient(
      begin: Alignment.bottomCenter,
      end: Alignment.topCenter,
      colors: chartColors.bgColor,
    );
    Rect mainRect = Rect.fromLTRB(0, 0, mMainRect.width, mMainRect.height + mTopPadding);
    canvas.drawRect(mainRect, mBgPaint..shader = mBgGradient.createShader(mainRect));

    if (mVolRect != null) {
      Rect volRect = Rect.fromLTRB(0, mVolRect!.top - mChildPadding, mVolRect!.width, mVolRect!.bottom);
      canvas.drawRect(volRect, mBgPaint..shader = mBgGradient.createShader(volRect));
    }

    if (mSecondaryRect != null) {
      Rect secondaryRect =
          Rect.fromLTRB(0, mSecondaryRect!.top - mChildPadding, mSecondaryRect!.width, mSecondaryRect!.bottom);
      canvas.drawRect(secondaryRect, mBgPaint..shader = mBgGradient.createShader(secondaryRect));
    }
    Rect dateRect = Rect.fromLTRB(0, size.height - mBottomPadding, size.width, size.height);
    canvas.drawRect(dateRect, mBgPaint..shader = mBgGradient.createShader(dateRect));
  }

  @override
  void drawGrid(canvas) {
    if (!hideGrid) {
      mMainRenderer.drawGrid(canvas, mGridRows, mGridColumns);
      mVolRenderer?.drawGrid(canvas, mGridRows, mGridColumns);
      mSecondaryRenderer?.drawGrid(canvas, mGridRows, mGridColumns);
    }
  }

  @override
  void drawChart(Canvas canvas, Size size) {
    canvas.save();
    canvas.translate(mTranslateX * scaleX, 0.0);
    canvas.scale(scaleX, 1.0);
    for (int i = mStartIndex; datas != null && i <= mStopIndex; i++) {
      KLineEntity? curPoint = datas?[i];
      if (curPoint == null) continue;
      KLineEntity lastPoint = i == 0 ? curPoint : datas![i - 1];
      double curX = getX(i);
      double lastX = i == 0 ? curX : getX(i - 1);

      mMainRenderer.drawChart(lastPoint, curPoint, lastX, curX, size, canvas);
      mVolRenderer?.drawChart(lastPoint, curPoint, lastX, curX, size, canvas);
      mSecondaryRenderer?.drawChart(lastPoint, curPoint, lastX, curX, size, canvas);
    }

    if ((isLongPress == true || (isTapShowInfoDialog && isOnTap)) && isTrendLine == false) {
      drawCrossLine(canvas, size);
    }
    if (isTrendLine == true) drawTrendLines(canvas, size);
    canvas.restore();

    // 在恢复canvas状态后绘制绘图工具，避免受到缩放和平移影响
    if (drawingToolManager != null) {
      // 创建正确的坐标转换函数
      double getXForDrawing(double x) {
        // x 应该是屏幕坐标，直接返回
        return x;
      }

      double getYForDrawing(double y) {
        // y 是屏幕坐标，直接返回
        return y;
      }

      debugPrint('ChartPainter.drawChart: 开始绘制绘图工具');
      drawingToolManager!.drawTools(canvas, size, 1.0, 0.0, getXForDrawing, getYForDrawing);
    }
  }

  @override
  void drawVerticalText(canvas) {
    var textStyle = getTextStyle(chartColors.defaultTextColor);
    if (!hideGrid) {
      mMainRenderer.drawVerticalText(canvas, textStyle, mGridRows);
    }
    mVolRenderer?.drawVerticalText(canvas, textStyle, mGridRows);
    mSecondaryRenderer?.drawVerticalText(canvas, textStyle, mGridRows);
  }

  @override
  void drawDate(Canvas canvas, Size size) {
    if (datas == null) return;

    // Check if we should use custom 5-day date labels
    // Only use custom labels if chart is in default state (no user interaction)
    if (chartStyle.is5DayChart &&
        chartStyle.custom5DayDateLabels != null &&
        chartStyle.custom5DayDateLabels!.isNotEmpty &&
        isInDefaultState) {
      _drawCustom5DayDateLabels(canvas, size);
      return;
    }

    // Check if we should use custom intraday labels
    // Only use custom labels if chart is in default state (no user interaction)
    bool shouldUseCustomLabels = isShowingCustomLabels;

    if (shouldUseCustomLabels) {
      _drawCustomIntradayLabels(canvas, size);
      return;
    }

    double columnSpace = size.width / mGridColumns;
    double startX = getX(mStartIndex) - mPointWidth / 2;
    double stopX = getX(mStopIndex) + mPointWidth / 2;
    double x = 0.0;
    double y = 0.0;
    for (var i = 0; i <= mGridColumns; ++i) {
      double translateX = xToTranslateX(columnSpace * i);

      if (translateX >= startX && translateX <= stopX) {
        int index = indexOfTranslateX(translateX);

        if (datas?[index] == null) continue;
        TextPainter tp = getTextPainter(getDate(datas![index].time, intradayTimeOnly: true), null);
        y = size.height - (mBottomPadding - tp.height) / 2 - tp.height;
        x = columnSpace * i - tp.width / 2;
        // Prevent date text out of canvas
        if (x < 0) x = 0;
        if (x > size.width - tp.width) x = size.width - tp.width;
        tp.paint(canvas, Offset(x, y));
      }
    }

//    double translateX = xToTranslateX(0);
//    if (translateX >= startX && translateX <= stopX) {
//      TextPainter tp = getTextPainter(getDate(datas[mStartIndex].id));
//      tp.paint(canvas, Offset(0, y));
//    }
//    translateX = xToTranslateX(size.width);
//    if (translateX >= startX && translateX <= stopX) {
//      TextPainter tp = getTextPainter(getDate(datas[mStopIndex].id));
//      tp.paint(canvas, Offset(size.width - tp.width, y));
//    }
  }

  /// Draw custom intraday time labels with market-specific positioning
  void _drawCustomIntradayLabels(Canvas canvas, Size size) {
    final labels = chartStyle.customIntradayLabels!;
    final labelCount = labels.length;
    final marketType = chartStyle.marketType ?? 'CN';

    if (labelCount == 0) return;

    double y = size.height - (mBottomPadding - 10) / 2 - 10;

    // Use full available width
    final availableWidth = size.width;

    for (int i = 0; i < labelCount; i++) {
      final label = labels[i];
      TextPainter tp = getTextPainter(label, null);

      double x = _calculateLabelPosition(i, labelCount, availableWidth, tp.width, marketType);

      if (x < 0) x = 0;
      if (x > availableWidth - tp.width) x = availableWidth - tp.width;

      tp.paint(canvas, Offset(x, y));
    }
  }

  /// Check if chart is in default state (no user interaction)
  bool get isInDefaultState {
    return (scaleX - 1.0).abs() < 0.00001 && scrollX.abs() < 0.00001;
  }

  /// Check if chart is currently showing custom intraday labels
  bool get isShowingCustomLabels {
    return chartStyle.customIntradayLabels != null && chartStyle.customIntradayLabels!.isNotEmpty && isInDefaultState;
  }

  /// Calculate label position based on market type and trading hours
  double _calculateLabelPosition(
      int index, int labelCount, double availableWidth, double textWidth, String marketType) {
    if (labelCount == 1) {
      return availableWidth / 2 - textWidth / 2;
    }

    switch (marketType) {
      case 'US':
        // US market: continuous trading 21:30-04:00 (6.5 hours)
        // No lunch break
        return switch (index) {
          0 => 0, // 21:30 at start
          1 => availableWidth * 0.31 - textWidth / 2, // 23:30 at ~31% (2h/6.5h)
          2 => availableWidth - textWidth, // 04:00 at end
          _ => (availableWidth * index / (labelCount - 1)) - textWidth / 2,
        };

      case 'HK':
        // HK market: 9:30-12:00 (2.5h) + 13:00-16:00 (3h) = 5.5h total
        return switch (index) {
          0 => 0, // 9:30 at start
          1 => availableWidth * 0.45 - textWidth / 2, // 12:00/13:00 at ~45% (2.5h/5.5h)
          2 => availableWidth - textWidth, // 16:00 at end
          _ => (availableWidth * index / (labelCount - 1)) - textWidth / 2,
        };

      case 'CN':
      default:
        // CN market: 9:30-11:30 (2h) + 13:00-15:00 (2h) = 4h total
        return switch (index) {
          0 => 0, // 9:30 at start
          1 => availableWidth * 0.5 - textWidth / 2, // 11:30/13:00 at middle (2h/4h)
          2 => availableWidth - textWidth, // 15:00 at end
          _ => (availableWidth * index / (labelCount - 1)) - textWidth / 2,
        };
    }
  }

  @override
  void drawCrossLineText(Canvas canvas, Size size) {
    var index = calculateSelectedX(selectX);
    KLineEntity point = getItem(index);

    double w1 = 5;
    double w2 = 3;
    double y = getMainY(point.close);
    bool isLeft = translateXtoX(getX(index)) >= mWidth / 2;

    // Always draw price label on the left side
    _drawCrosshairPriceLabel(canvas, point.close, y, w1, w2);

    // Conditionally draw percentage label on the right side
    if (chartStyle.showPercentageLabels && chartStyle.referencePrice != null && chartStyle.referencePrice! > 0) {
      _drawCrosshairPercentageLabel(canvas, point.close, y, w1, w2);
    }

    // Draw date/time label at bottom
    TextPainter dateTp =
        getTextPainter(getDate(point.time, intradayTimeOnly: true), chartColors.crosshairLabelTextColor);
    double textWidth = dateTp.width;
    double textHeight = dateTp.height;
    double r = textHeight / 2;
    double x = translateXtoX(getX(index));
    y = size.height - mBottomPadding;

    if (x < textWidth + 2 * w1) {
      x = 1 + textWidth / 2 + w1;
    } else if (mWidth - x < textWidth + 2 * w1) {
      x = mWidth - 1 - textWidth / 2 - w1;
    }
    double baseLine = textHeight / 2;
    canvas.drawRect(Rect.fromLTRB(x - textWidth / 2 - w1, y, x + textWidth / 2 + w1, y + baseLine + r),
        crosshairLabelBackgroundPaint);
    canvas.drawRect(
        Rect.fromLTRB(x - textWidth / 2 - w1, y, x + textWidth / 2 + w1, y + baseLine + r), crosshairLabelBorderPaint);

    dateTp.paint(canvas, Offset(x - textWidth / 2, y));
    //长按显示这条数据详情
    sink?.add(InfoWindowEntity(point, isLeft: isLeft));
  }

  /// Draw price label on the left side of crosshair
  void _drawCrosshairPriceLabel(Canvas canvas, double price, double y, double w1, double w2) {
    TextPainter tp = getTextPainter(price.toStringAsFixed(3), chartColors.crosshairLabelTextColor);
    double textHeight = tp.height;
    double textWidth = tp.width;
    double r = textHeight / 2 + w2;

    // Always position on the left side
    double x = 1;
    Path path = Path();
    path.moveTo(x, y - r);
    path.lineTo(x, y + r);
    path.lineTo(textWidth + 2 * w1, y + r);
    path.lineTo(textWidth + 2 * w1 + w2, y);
    path.lineTo(textWidth + 2 * w1, y - r);
    path.close();
    canvas.drawPath(path, crosshairLabelBackgroundPaint);
    canvas.drawPath(path, crosshairLabelBorderPaint);
    tp.paint(canvas, Offset(x + w1, y - textHeight / 2));
  }

  /// Draw percentage label on the right side of crosshair
  void _drawCrosshairPercentageLabel(Canvas canvas, double currentPrice, double y, double w1, double w2) {
    double percentage = ((currentPrice - chartStyle.referencePrice!) / chartStyle.referencePrice!) * 100;
    String percentageText = "${percentage.toStringAsFixed(2)}%";

    TextPainter tp = getTextPainter(percentageText, chartColors.crosshairLabelTextColor);
    double textHeight = tp.height;
    double textWidth = tp.width;
    double r = textHeight / 2 + w2;

    // Always position on the right side
    double x = mWidth - textWidth - 1 - 2 * w1 - w2;
    Path path = Path();
    path.moveTo(x, y);
    path.lineTo(x + w2, y + r);
    path.lineTo(mWidth - 2, y + r);
    path.lineTo(mWidth - 2, y - r);
    path.lineTo(x + w2, y - r);
    path.close();
    canvas.drawPath(path, crosshairLabelBackgroundPaint);
    canvas.drawPath(path, crosshairLabelBorderPaint);
    tp.paint(canvas, Offset(x + w1 + w2, y - textHeight / 2));
  }

  @override
  void drawText(Canvas canvas, KLineEntity data, double x) {
    //长按显示按中的数据
    if (isLongPress || (isTapShowInfoDialog && isOnTap)) {
      var index = calculateSelectedX(selectX);
      data = getItem(index);
    }
    //松开显示最后一条数据
    mMainRenderer.drawText(canvas, data, x);
    mVolRenderer?.drawText(canvas, data, x);
    mSecondaryRenderer?.drawText(canvas, data, x);
  }

  @override
  void drawMaxAndMin(Canvas canvas) {
    if (isLine == true) return;
    double lineSize = 20;
    double lineToTextOffset = 5;

    Paint linePaint = Paint()
      ..strokeWidth = 1
      ..color = chartColors.minColor;

    //绘制最大值和最小值
    double x = translateXtoX(getX(mMainMinIndex));
    double y = getMainY(mMainLowMinValue);
    if (x < mWidth / 2) {
      //画右边
      TextPainter tp = getTextPainter(
        mMainLowMinValue.toStringAsFixed(fixedLength),
        chartColors.minColor,
      );

      canvas.drawLine(
        Offset(x, y),
        Offset(x + lineSize, y),
        linePaint,
      );

      tp.paint(
        canvas,
        Offset(
          x + lineSize + lineToTextOffset,
          y - tp.height / 2,
        ),
      );
    } else {
      TextPainter tp = getTextPainter(
        mMainLowMinValue.toStringAsFixed(fixedLength),
        chartColors.minColor,
      );

      canvas.drawLine(
        Offset(x, y),
        Offset(x - lineSize, y),
        linePaint,
      );

      tp.paint(
        canvas,
        Offset(
          x - tp.width - lineSize - lineToTextOffset,
          y - tp.height / 2,
        ),
      );
    }
    x = translateXtoX(getX(mMainMaxIndex));
    y = getMainY(mMainHighMaxValue);
    if (x < mWidth / 2) {
      //画右边
      TextPainter tp = getTextPainter(
        mMainHighMaxValue.toStringAsFixed(fixedLength),
        chartColors.maxColor,
      );

      canvas.drawLine(
        Offset(x, y),
        Offset(x + lineSize, y),
        linePaint,
      );

      tp.paint(
        canvas,
        Offset(
          x + lineSize + lineToTextOffset,
          y - tp.height / 2,
        ),
      );
    } else {
      TextPainter tp = getTextPainter(
        mMainHighMaxValue.toStringAsFixed(fixedLength),
        chartColors.maxColor,
      );

      canvas.drawLine(
        Offset(x, y),
        Offset(x - lineSize, y),
        linePaint,
      );

      tp.paint(
        canvas,
        Offset(
          x - tp.width - lineSize - lineToTextOffset,
          y - tp.height / 2,
        ),
      );
    }
  }

  @override
  void drawNowPrice(Canvas canvas) {
    if (!showNowPrice) {
      return;
    }

    if (datas == null) {
      return;
    }

    double value = datas!.last.close;
    double y = getMainY(value);

    //视图展示区域边界值绘制
    if (y > getMainY(mMainLowMinValue)) {
      y = getMainY(mMainLowMinValue);
    }

    if (y < getMainY(mMainHighMaxValue)) {
      y = getMainY(mMainHighMaxValue);
    }

    nowPricePaint.color = value >= datas!.last.open ? chartColors.nowPriceUpColor : chartColors.nowPriceDnColor;
    //先画横线
    double startX = 0;
    final max = -mTranslateX + mWidth / scaleX;
    final space = chartStyle.nowPriceLineSpan + chartStyle.nowPriceLineLength;
    while (startX < max) {
      canvas.drawLine(Offset(startX, y), Offset(startX + chartStyle.nowPriceLineLength, y), nowPricePaint);
      startX += space;
    }
    //再画背景和文本
    TextPainter tp = getTextPainter(value.toStringAsFixed(fixedLength), chartColors.nowPriceTextColor);

    double offsetX;
    switch (verticalTextAlignment) {
      case VerticalTextAlignment.left:
        offsetX = 0;
        break;
      case VerticalTextAlignment.right:
        offsetX = mWidth - tp.width;
        break;
    }

    double top = y - tp.height / 2;
    canvas.drawRect(Rect.fromLTRB(offsetX, top, offsetX + tp.width, top + tp.height), nowPricePaint);
    tp.paint(canvas, Offset(offsetX, top));
  }

//For TrendLine
  void drawTrendLines(Canvas canvas, Size size) {
    var index = calculateSelectedX(selectX);
    Paint paintY = Paint()
      ..color = Colors.orange
      ..strokeWidth = 1
      ..isAntiAlias = true;
    double x = getX(index);
    trendLineX = x;

    double y = selectY;
    // getMainY(point.close);

    // k线图竖线
    canvas.drawLine(Offset(x, mTopPadding), Offset(x, size.height - mBottomPadding), paintY);
    Paint paintX = Paint()
      ..color = Colors.orangeAccent
      ..strokeWidth = 1
      ..isAntiAlias = true;
    Paint paint = Paint()
      ..color = Colors.orange
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;
    canvas.drawLine(Offset(-mTranslateX, y), Offset(-mTranslateX + mWidth / scaleX, y), paintX);
    if (scaleX >= 1) {
      canvas.drawOval(Rect.fromCenter(center: Offset(x, y), height: 15.0 * scaleX, width: 15.0), paint);
    } else {
      canvas.drawOval(Rect.fromCenter(center: Offset(x, y), height: 10.0, width: 10.0 / scaleX), paint);
    }
    if (lines.isNotEmpty) {
      for (var element in lines) {
        var y1 = -((element.p1.dy - 35) / element.scale) + element.maxHeight;
        var y2 = -((element.p2.dy - 35) / element.scale) + element.maxHeight;
        var a = (trendLineMax! - y1) * trendLineScale! + trendLineContentRec!;
        var b = (trendLineMax! - y2) * trendLineScale! + trendLineContentRec!;
        var p1 = Offset(element.p1.dx, a);
        var p2 = Offset(element.p2.dx, b);
        canvas.drawLine(
            p1,
            element.p2 == Offset(-1, -1) ? Offset(x, y) : p2,
            Paint()
              ..color = Colors.yellow
              ..strokeWidth = 2);
      }
    }
  }

  ///画交叉线
  @override
  void drawCrossLine(Canvas canvas, Size size) {
    var index = calculateSelectedX(selectX);
    KLineEntity point = getItem(index);

    // 创建虚线画笔 - 竖线使用与横线相同的颜色
    Paint paintY = Paint()
      ..color = chartColors.hCrossColor // 使用与横线相同的颜色
      ..strokeWidth = chartStyle.hCrossWidth // 使用与横线相同的宽度
      ..isAntiAlias = true
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    double x = getX(index);
    double y = getMainY(point.close);

    // k线图竖线 - 使用虚线
    _drawDashedLine(
      canvas,
      Offset(x, mTopPadding),
      Offset(x, size.height - mBottomPadding),
      paintY,
    );

    Paint paintX = Paint()
      ..color = chartColors.hCrossColor
      ..strokeWidth = chartStyle.hCrossWidth
      ..isAntiAlias = true
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // k线图横线 - 使用虚线
    _drawDashedLine(
      canvas,
      Offset(-mTranslateX, y),
      Offset(-mTranslateX + mWidth / scaleX, y),
      paintX,
    );

    if (scaleX >= 1) {
      canvas.drawOval(Rect.fromCenter(center: Offset(x, y), height: 2.0 * scaleX, width: 2.0), paintX);
    } else {
      canvas.drawOval(Rect.fromCenter(center: Offset(x, y), height: 2.0, width: 2.0 / scaleX), paintX);
    }
  }

  /// 绘制虚线
  void _drawDashedLine(Canvas canvas, Offset start, Offset end, Paint paint) {
    const double dashLength = 3.0; // 增加虚线长度
    const double dashSpace = 3.0; // 增加间距

    final double distance = (end - start).distance;
    final Offset direction = (end - start) / distance;

    double currentDistance = 0.0;
    bool drawDash = true;

    while (currentDistance < distance) {
      final double segmentLength = drawDash ? dashLength : dashSpace;
      final double nextDistance = (currentDistance + segmentLength).clamp(0.0, distance);

      if (drawDash) {
        final Offset segmentStart = start + direction * currentDistance;
        final Offset segmentEnd = start + direction * nextDistance;
        canvas.drawLine(segmentStart, segmentEnd, paint);
      }

      currentDistance = nextDistance;
      drawDash = !drawDash;
    }
  }

  TextPainter getTextPainter(text, color) {
    color ??= chartColors.defaultTextColor;
    TextSpan span = TextSpan(text: "$text", style: getTextStyle(color));
    TextPainter tp = TextPainter(text: span, textDirection: TextDirection.ltr);
    tp.layout();
    return tp;
  }

  String getDate(int? date, {bool intradayTimeOnly = false}) {
    if (date == null) return '';

    // Timestamps have been adjusted with market timezone offset in data adapter
    // Display them directly
    DateTime dt = DateTime.fromMillisecondsSinceEpoch(
      date,
      isUtc: true,
    );

    // Check if this is a 5-day chart and we want date+time
    if (intradayTimeOnly && chartStyle.is5DayChart) {
      String month = dt.month.toString().padLeft(2, '0');
      String day = dt.day.toString().padLeft(2, '0');
      String hours = dt.hour.toString().padLeft(2, '0');
      String minutes = dt.minute.toString().padLeft(2, '0');
      return "$month/$day $hours:$minutes";
    }

    // Check if this is an intraday chart and we want time only
    if (intradayTimeOnly && _isIntradayFormat()) {
      String hours = dt.hour.toString().padLeft(2, '0');
      String minutes = dt.minute.toString().padLeft(2, '0');
      return "$hours:$minutes";
    }

    return dateFormat(dt, mFormats);
  }

  /// Check if current format is intraday (contains hour and minute)
  bool _isIntradayFormat() {
    return mFormats.contains(HH) && mFormats.contains(nn);
  }

  /// Draw simple 5-day date labels with accurate positioning
  void _drawCustom5DayDateLabels(Canvas canvas, Size size) {
    final labels = chartStyle.custom5DayDateLabels!;
    final positions = chartStyle.custom5DayLabelPositions;
    if (labels.isEmpty) return;

    final y = size.height - (mBottomPadding - 10) / 2 - 10;

    // Use accurate positioning if available, otherwise fall back to even distribution
    if (positions != null && positions.length == labels.length) {
      // Use calculated positions based on actual data distribution
      for (int i = 0; i < labels.length; i++) {
        final label = labels[i];
        TextPainter tp = getTextPainter(label, null);

        double x = positions[i] - tp.width / 2;

        // Keep labels within bounds
        if (x < 0) x = 0;
        if (x > size.width - tp.width) {
          x = size.width - tp.width;
        }

        tp.paint(canvas, Offset(x, y));
      }
    } else {
      // Fallback to even distribution
      final availableWidth = size.width * 0.9;
      final leftMargin = size.width * 0.05;

      for (int i = 0; i < labels.length; i++) {
        final label = labels[i];
        TextPainter tp = getTextPainter(label, null);

        double x;
        if (labels.length == 1) {
          x = leftMargin + (availableWidth - tp.width) / 2;
        } else {
          x = leftMargin + (availableWidth * i / (labels.length - 1)) - tp.width / 2;
        }

        // Keep labels within bounds
        if (x < 0) x = 0;
        if (x > size.width - tp.width) {
          x = size.width - tp.width;
        }

        tp.paint(canvas, Offset(x, y));
      }
    }
  }

  double getMainY(double y) => mMainRenderer.getY(y);

  /// 点是否在SecondaryRect中
  bool isInSecondaryRect(Offset point) {
    return mSecondaryRect?.contains(point) ?? false;
  }

  /// 点是否在MainRect中
  bool isInMainRect(Offset point) {
    return mMainRect.contains(point);
  }
}
