import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/kline_option.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class KlineSelectorBar extends StatelessWidget {
  const KlineSelectorBar({
    super.key,
    required this.primaryOptions,
    required this.secondaryOptions,
    required this.selectedPrimary,
    required this.selectedSecondary,
    required this.status,
    required this.onPrimaryTap,
    required this.onSecondaryChanged,
    this.horizontalPadding = const EdgeInsets.fromLTRB(10, 0, 0, 0),
    this.itemSpacing = 20,
    this.showSecondaryUnderline = true,
  });

  final List<KlineOption> primaryOptions;
  final List<KlineOption> secondaryOptions;
  final KlineOption? selectedPrimary;
  final KlineOption? selectedSecondary;
  final DataStatus status;
  final EdgeInsets horizontalPadding;
  final double itemSpacing;
  final bool showSecondaryUnderline;

  final void Function(KlineOption option) onPrimaryTap;
  final void Function(KlineOption option) onSecondaryChanged;

  @override
  Widget build(BuildContext context) {
    final isLoading = status == DataStatus.loading;
    final bool isSecondaryActive = secondaryOptions.any((e) => e.id == selectedPrimary?.id);

    return AbsorbPointer(
      absorbing: isLoading,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        padding: horizontalPadding,
        child: Row(
          spacing: 10,
          children: [
            Row(
              spacing: itemSpacing,
              mainAxisAlignment: MainAxisAlignment.start,
              children: primaryOptions.map((option) {
                final bool isSelected = selectedPrimary?.id == option.id;
                return Bounceable(
                  onTap: () => onPrimaryTap(option),
                  child: Container(
                    color: context.theme.cardColor,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      spacing: 4,
                      children: [
                        Text(
                          option.label.tr(),
                          style: context.textTheme.primary.fs12.w700.copyWith(
                            color: isLoading
                                ? context.colorTheme.textRegular.withValues(alpha: 0.3)
                                : isSelected
                                    ? context.theme.primaryColor
                                    : null,
                          ),
                        ),
                        Opacity(
                          opacity: isSelected ? 1 : 0,
                          child: Container(
                            width: 30.gw,
                            height: 2.gw,
                            color: isLoading
                                ? context.colorTheme.textRegular.withValues(alpha: 0.3)
                                : context.theme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
            if (secondaryOptions.isNotEmpty)
              _SecondaryDropdown(
                options: secondaryOptions,
                selected: selectedSecondary ?? secondaryOptions.first,
                isLoading: isLoading,
                showUnderline: isSecondaryActive,
                onChanged: onSecondaryChanged,
              ),
          ],
        ),
      ),
    );
  }
}

class _SecondaryDropdown extends StatelessWidget {
  const _SecondaryDropdown({
    required this.options,
    required this.selected,
    required this.isLoading,
    required this.onChanged,
    this.showUnderline = true,
  });

  final List<KlineOption> options;
  final KlineOption selected;
  final bool isLoading;
  final bool showUnderline;
  final void Function(KlineOption option) onChanged;

  @override
  Widget build(BuildContext context) {
    final color = isLoading ? context.colorTheme.textRegular.withValues(alpha: 0.3) : context.theme.primaryColor;
    final label = selected.label.tr();
    final textStyle = context.textTheme.primary.fs12.w700.copyWith(color: color);
    final containerWidth = 50.gw;
    final chevronWidth = 14.gw;
    final horizontalPadding = 8.gw; // approx inner spacing
    final textPainter = TextPainter(
      text: TextSpan(text: label, style: textStyle),
      textDirection: Directionality.of(context),
      maxLines: 1,
      ellipsis: '…',
    )..layout(maxWidth: (containerWidth - (horizontalPadding * 2)).clamp(0, double.infinity));
    final double underlineWidth = (textPainter.width + chevronWidth).clamp(30.gw, (containerWidth - horizontalPadding));

    return Container(
      color: Colors.transparent,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        spacing: 4,
        children: [
          SizedBox(
            width: 50.gw,
            height: 30.gw,
            child: PopupMenuButton<KlineOption>(
              tooltip: '',
              color: context.theme.cardColor,
              position: PopupMenuPosition.over,
              onSelected: (opt) => onChanged(opt),
              itemBuilder: (ctx) => options
                  .map(
                    (e) => PopupMenuItem<KlineOption>(
                      value: e,
                      child: Text(
                        e.label.tr(),
                        style: context.textTheme.regular,
                      ),
                    ),
                  )
                  .toList(),
              child: Container(
                decoration: BoxDecoration(color: Colors.transparent, borderRadius: BorderRadius.circular(5.gr)),
                alignment: Alignment.center,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Flexible(
                      child: Text(
                        selected.label.tr(),
                        overflow: TextOverflow.ellipsis,
                        style: context.textTheme.primary.fs12.w700.copyWith(color: color),
                      ),
                    ),
                    SizedBox(width: 4.gw),
                    Icon(Icons.keyboard_arrow_down, size: 14.gw, color: color),
                  ],
                ),
              ),
            ),
          ),
          Opacity(
            opacity: showUnderline ? 1 : 0,
            child: Align(
              alignment: Alignment.center,
              child: Container(
                width: underlineWidth,
                height: 2.gw,
                color: color,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
