import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class Instrument {
  final String instrument;
  const Instrument({required this.instrument});
  String get market => instrument.split('|')[0];
  String get securityType => instrument.split('|')[1];
  String get symbol => instrument.split('|')[2];
  MainMarketType get marketType => getMainMarketType(market);
  MarketCategory get marketCategory => MarketCategory.fromMarketType(market);
}
