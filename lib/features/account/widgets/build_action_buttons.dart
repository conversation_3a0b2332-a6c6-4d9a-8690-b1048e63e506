import 'package:flutter/material.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';

class BuildActionButton extends StatelessWidget {
  const BuildActionButton({
    super.key,
    required this.label,
    required this.icon,
    this.onTap,
  });

  final String label;
  final String icon;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    double? height;
    double? width;
    bool? hideColor;
    switch (AppConfig.instance.skinStyle) {
      case AppSkinStyle.kGP:
        hideColor = true;
        width = 30;
        height = 30;
      default:
        hideColor = false;
        width = 25;
        height = 25;
    }
    return Bounceable(
      onTap: onTap,
      child: Column(
        children: [
          IconButton(
            onPressed: onTap,
            icon: IconHelper.loadAsset(
              icon,
              color: hideColor ? null : context.theme.primaryColor,
              width: width,
              height: height,
            ),
          ),
          SizedBox(
            child: Text(
              label,
              style: context.textTheme.regular.fs11.w500.copyWith(
                color: switch (AppConfig.instance.skinStyle) {
                  AppSkinStyle.kTemplateD => context.colorTheme.textTitle,
                  _ => context.colorTheme.textPrimary
                },
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }
}
