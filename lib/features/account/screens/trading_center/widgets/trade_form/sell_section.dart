import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/trade/trade_handling_fee_config_entity.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/confirm_dialog_widget.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/position_dropdown.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_reponse/stock_info_response.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/exchange_rate/exchange_rate_cubit.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';

class SellSection extends StatefulWidget {
  const SellSection({super.key, required this.sellType});

  final SellType sellType;

  @override
  State<SellSection> createState() => _SellSectionState();
}

class _SellSectionState extends State<SellSection> {
  @override
  Widget build(BuildContext context) {
    return BlocSelector<
        TradingCubit,
        TradingState,
        ({
          StockInfoData? stockInfoData,
          double limit,
          double quantity,
          int maximumQuantityToSell,
          TradeDirection tradeDirection,
          List<TradeHandlingFeeConfigEntity> chargeList,
          FTradeAcctOrderRecords? selectedPosition,
          ContractSummaryData? contract,
          bool isIndexTrading,
          int tradeUnit,
        })>(
      selector: (state) => (
        stockInfoData: state.stockInfo,
        limit: state.limit,
        quantity: state.quantity,
        maximumQuantityToSell: state.accountBalance.toInt(),
        tradeDirection: state.tradeDirection,
        chargeList: state.tradeDirection == TradeDirection.buy ? state.calculateConfigBuy : state.calculateConfigSell,
        selectedPosition: state.selectedPositionShort,
        contract: state.contract,
        isIndexTrading: state.isIndexTrading,
        tradeUnit: state.indexStockData?.tradeUnit ?? 0,
      ),
      builder: (context, state) {
        final cubit = context.read<TradingCubit>();
        double amount = 0;
        // if (state.stockInfoData?.latestPrice == state.limit) {
        //   amount = (state.stockInfoData?.latestPrice ?? 0) * state.quantity;
        // } else {
        //   amount = state.limit * state.quantity;
        // }
        bool enabled = true;
        String sellLabel = '';
        String availableLabel = '';
        String lotLabel = '';
        double available = cubit.getQuantityByFraction(OrderFraction.full);
        int tradeType = 1;
        final dealPrice = cubit.dealPrice;
        final marketType = getMainMarketType(state.stockInfoData?.market ?? '');
        final order = state.quantity * dealPrice;
        amount = order;
        double fee = cubit.calculateFee(amount);
        double totalAmount = 0;
        final positions = context.select<AccountCubit, List<FTradeAcctOrderRecords>?>(
                (state) => state.state.currentPositions?.records) ??
            [];
        cubit.getAvailableQuantity(positions, false);
        ({int long, int short}) getAvailableToClose(List<FTradeAcctOrderRecords>? positions) {
          if (positions == null || positions.isEmpty) return (long: 0, short: 0);
          // Filter positions based on tradeType
          final longPositions = positions.where((p) => p.tradeType == 1).toList();
          final shortPositions = positions.where((p) => p.tradeType == 2).toList();
          final longSum = longPositions.fold(0, (sum, p) => sum + (p.restNum).toInt());
          final shortSum = shortPositions.fold(0, (sum, p) => sum + (p.restNum).toInt());
          return (long: longSum, short: shortSum);
        }

        if (state.isIndexTrading) {
          lotLabel = 'lotForStockIndex'.tr();
        } else {
          lotLabel = 'lotForSecurities'.tr();
        }
        if (marketType == MainMarketType.cnShares && !state.isIndexTrading) {
          sellLabel = 'sell'.tr();
          availableLabel = 'availableToSell'.tr();
          totalAmount = amount - fee;
          enabled = context.read<TradingCubit>().isSellingEnabled();
          // available = state.maximumQuantityToSell;
        } else {
          if (state.tradeDirection == TradeDirection.buy) {
            sellLabel = 'openShort'.tr(); // 开空
            // available = getAvailableToClose(positions).short.toDouble();
            availableLabel = 'availableToOpen'.tr();
            tradeType = 2;
            totalAmount = amount + fee;
            enabled = context.read<TradingCubit>().isSellingEnabled(isSellShort: true);
            enabled = available > 0;
          } else {
            sellLabel = 'sellShort'.tr(); // 平空

            available = getAvailableToClose(positions).short.toDouble();
            if (state.selectedPosition != null) {
              available = state.selectedPosition?.restNum ?? 0;
            }
            availableLabel = 'availableToClose'.tr();
            totalAmount = amount - fee;
            tradeType = 2;
            enabled = available > 0;
          }
        }
        final exchangeRate =
            context.read<ExchangeRateCubit>().getCurrencyRateConfig(state.stockInfoData?.currency ?? '');
        return Column(
          spacing: 8,
          children: [
            if (state.isIndexTrading && state.tradeDirection == TradeDirection.sell)
              PositionDropdown(
                positions: positions,
                tradeType: tradeType,
                selectedPosition: state.selectedPosition,
                onPositionSelected: (value) {
                  if (value != null) {
                    cubit.setSelectedPositionSellLong(value);
                  }
                },
              ),
            AmountRow(
              title: availableLabel,
              amount: available,
              suffix: lotLabel,
              fontSize: 13.gr,
              fractionDigits: state.isIndexTrading ? 1 : 0,
            ),
            AmountRow(
                title: 'service_charge'.tr(), amount: fee, fontSize: 13.gr, currency: state.stockInfoData?.currency),
            AmountRow(
                title: 'orderAmount'.tr(), amount: amount, fontSize: 13.gr, currency: state.stockInfoData?.currency),
            AmountRow(
              title: 'totalPrice'.tr(),
              amount: totalAmount,
              fontSize: 13.gr,
              currency: state.stockInfoData?.currency,
              showTotalToolTip: true,
            ),
            if (marketType != MainMarketType.cnShares && state.contract == null)
              Row(
                children: [
                  Spacer(),
                  AnimatedFlipCounter(
                    prefix: '≈ ',
                    duration: const Duration(milliseconds: 500),
                    suffix: ' ${exchangeRate.currencyBase}',
                    thousandSeparator: ',',
                    fractionDigits: 2,
                    textStyle: context.textTheme.primary.w800.fs13.ffAkz,
                    value: totalAmount / exchangeRate.rate,
                  )
                ],
              ),
            BlocSelector<TradingCubit, TradingState, bool>(
              selector: (state) => state.createSellOrderStatus == DataStatus.loading,
              builder: (context, isLoading) {
                return CommonButton(
                  height: 30.gw,
                  enable: enabled,
                  showLoading: isLoading,
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (_) {
                        return BlocProvider.value(
                          value: cubit,
                          child: ConfirmDialogWidget(
                            tradeType: tradeType,
                            directionLabel: sellLabel,
                            marketType: marketType,
                            calculateConfig: state.chargeList,
                            selectedPosition: state.selectedPosition,
                            isBuySection: false,
                          ),
                        );
                      },
                    );
                  },
                  title: sellLabel,
                  borderColor: context.downColor,
                  textColor: context.theme.cardColor,
                  fontSize: 13.gr,
                  color: context.downColor,
                );
              },
            ),
          ],
        );
      },
    );
  }
}
