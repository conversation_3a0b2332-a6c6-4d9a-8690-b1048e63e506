import 'package:flutter/material.dart';
import 'package:flutter_chen_kchart/k_chart.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/chart_period_type.dart';

/// Theme adapter for integrating flutter_chen_kchart with our app's theme system
/// This ensures the chart colors match our app's design system
class ChenKChartThemeAdapter {
  /// Create ChartColors that match our app's theme
  static ChartColors createChartColors(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final colorTheme = context.colorTheme;

    // Set the global chart theme
    ChartThemeManager.setTheme(isDark ? ChartTheme.dark : ChartTheme.light);

    final chartColors = ChartColors();

    // Configure colors to match our app theme
    chartColors.upColor = const Color(0xFFD2544F); // Red for up/gain
    chartColors.dnColor = const Color(0xFF5DAF78); // Green for down/loss
    chartColors.kLineColor = const Color(0xFFD2544F); // Add this line for K-line color
    chartColors.gridColor = Colors.transparent; // Hide grid for cleaner look

    // Set solid color
    chartColors.lineFillColor = const Color(0xFF005af1).withValues(alpha: 0.1); // Solid blue with 10% opacity
    chartColors.lineFillInsideColor = const Color(0xFF005af1).withValues(alpha: 0.1); // Same color for solid fill
    chartColors.bgColor = [
      context.theme.cardColor,
      context.theme.cardColor,
    ];

    // MA line colors - using our brand colors
    chartColors.ma5Color = const Color(0xffE5B767); // Gold
    chartColors.ma10Color = const Color(0xff1FD1AC); // Teal
    chartColors.ma30Color = const Color(0xffB48CE3); // Purple

    // Volume and indicator colors
    chartColors.volColor = const Color(0xff4729AE); // Purple for volume
    chartColors.macdColor = const Color(0xff4729AE);
    chartColors.difColor = const Color(0xffC9B885);
    chartColors.deaColor = const Color(0xff6CB0A6);

    // KDJ colors
    chartColors.kColor = const Color(0xffC9B885);
    chartColors.dColor = const Color(0xff6CB0A6);
    chartColors.jColor = const Color(0xff9979C6);
    chartColors.rsiColor = const Color(0xffC9B885);

    // Text colors - use our theme colors
    chartColors.defaultTextColor = colorTheme.textPrimary;

    // Current price line colors
    chartColors.nowPriceUpColor = const Color(0xff4DAA90);
    chartColors.nowPriceDnColor = const Color(0xffC15466);
    chartColors.nowPriceTextColor = Colors.white;

    // Depth chart colors (for order book)
    chartColors.depthBuyColor = const Color(0xff60A893);
    chartColors.depthSellColor = const Color(0xffC15866);

    // Selection and crosshair colors
    chartColors.selectBorderColor = const Color(0xff6C7A86);
    chartColors.selectFillColor = isDark ? const Color(0xff0D1722) : const Color(0xffF5F5F5);

    // Info window colors
    chartColors.infoWindowNormalColor = colorTheme.textPrimary;
    chartColors.infoWindowTitleColor = colorTheme.textPrimary;
    chartColors.infoWindowUpColor = const Color(0xff00ff00);
    chartColors.infoWindowDnColor = const Color(0xffff0000);

    // Crosshair colors
    chartColors.hCrossColor = isDark ? Colors.white : Colors.black;
    chartColors.vCrossColor = isDark ? const Color(0x1Effffff) : const Color(0x1E000000);
    chartColors.crossTextColor = isDark ? Colors.white : Colors.black;

    // Min/max value colors
    chartColors.maxColor = colorTheme.textPrimary;
    chartColors.minColor = colorTheme.textPrimary;

    return chartColors;
  }

  /// Create ChartStyle that adapts to our app's responsive design
  static ChartStyle createChartStyle({
    required BuildContext context,
    required ChartPeriodType periodType,
    double? screenWidth,
    double? screenHeight,
    int? expectedDataPoints,
    TodaysTab? marketTab,
  }) {
    final mediaQuery = MediaQuery.of(context);
    final width = screenWidth ?? mediaQuery.size.width;
    final height = screenHeight ?? mediaQuery.size.height;

    final chartStyle = ChartStyle();

    // Responsive sizing based on screen dimensions
    final isTablet = width > 600;
    final isLargeScreen = height > 800;

    if (expectedDataPoints != null && expectedDataPoints > 0 && periodType.isIntradayType) {
      final availableWidth = width * 1;
      final calculatedPointWidth = availableWidth / expectedDataPoints;
      final calculatedCandleWidth = calculatedPointWidth * 0.75;

      // Ensure all data points fit in screen width without horizontal scrolling
      chartStyle.pointWidth = calculatedPointWidth.clamp(0.2, 10.0).toDouble();
      chartStyle.candleWidth = calculatedCandleWidth.clamp(0.2, 8.0).toDouble();
    } else {
      if (periodType.isIntradayType) {
        // Intraday charts - more compact
        chartStyle.pointWidth = isTablet ? 8.0 : 6.0;
        chartStyle.candleWidth = isTablet ? 6.0 : 4.0;
      } else {
        // Daily/weekly/monthly charts - more spacious
        chartStyle.pointWidth = isTablet ? 11.0 : 9.0;
        chartStyle.candleWidth = isTablet ? 8.5 : 6.5;
      }
    }

    // Adjust other dimensions
    chartStyle.candleLineWidth = 1.5;
    chartStyle.volWidth = chartStyle.candleWidth;
    chartStyle.macdWidth = 3.0;
    chartStyle.vCrossWidth = chartStyle.candleWidth;
    chartStyle.hCrossWidth = 0.5;

    // Responsive padding
    chartStyle.topPadding = isLargeScreen ? 30.0 : 20.0;
    chartStyle.bottomPadding = isLargeScreen ? 20.0 : 15.0;
    chartStyle.childPadding = isLargeScreen ? 12.0 : 8.0;

    // Grid configuration
    chartStyle.gridRows = 4;
    chartStyle.gridColumns = isTablet ? 6 : 4;

    // Current price line style
    chartStyle.nowPriceLineLength = 1;
    chartStyle.nowPriceLineSpan = 1;
    chartStyle.nowPriceLineWidth = 1;

    // Set custom intraday labels for market-specific trading hours
    if (periodType.isIntraday && marketTab != null) {
      chartStyle.customIntradayLabels = getIntradayLabelsForMarket(marketTab);
      chartStyle.marketType = getMarketTypeString(marketTab);
    }

    return chartStyle;
  }

  /// Configure percentage labels based on chart period type and reference price
  static ChartStyle configurePercentageLabels(
    ChartStyle chartStyle,
    ChartPeriodType periodType,
    double? originalClosePrice,
  ) {
    // Show percentage labels only for intraday, 1-day, and 5-day charts
    final shouldShowPercentage = periodType.isIntraday || periodType.isFiveDay || periodType == ChartPeriodType.daily;

    return chartStyle.copyWith(
      showPercentageLabels: shouldShowPercentage,
      referencePrice: originalClosePrice,
    );
  }

  /// Get appropriate time format for different chart types
  static List<String> getTimeFormat({
    required ChartPeriodType periodType,
    required bool isLine,
  }) {
    if (periodType.isIntradayType || isLine) {
      // Show only time (HH:mm) for intraday charts
      return [HH, ':', nn];
    } else {
      return TimeFormat.YEAR_MONTH_DAY;
    }
  }

  /// Configure chart theme based on app theme
  static void configureGlobalTheme(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    ChartThemeManager.setTheme(isDark ? ChartTheme.dark : ChartTheme.light);
  }

  /// Get MA day list based on chart type and screen size
  static List<int> getMADayList({
    required ChartPeriodType periodType,
    required double screenWidth,
  }) {
    if (periodType.isIntradayType) {
      // Fewer MA lines for intraday
      return screenWidth > 600 ? [5, 10, 20] : [5, 10];
    } else {
      // Full MA lines for daily/weekly/monthly
      return screenWidth > 600 ? [5, 10, 20, 30, 60] : [5, 10, 20, 30];
    }
  }

  /// Get main state based on preferences
  static MainState getMainState() {
    return MainState.MA;
  }

  /// Get market-specific intraday labels based on market type
  static List<String> getIntradayLabelsForMarket(TodaysTab marketTab) {
    return switch (marketTab) {
      TodaysTab.aShares => ['09:30', '11:30/13:00', '15:00'],
      TodaysTab.hkShares => ['09:30', '12:00/13:00', '16:00'],
      TodaysTab.usShares => ['21:31', '23:30', '04:00'],
    };
  }

  /// Get market type string for chart positioning logic
  static String getMarketTypeString(TodaysTab marketTab) {
    return switch (marketTab) {
      TodaysTab.aShares => 'CN',
      TodaysTab.hkShares => 'HK',
      TodaysTab.usShares => 'US',
    };
  }

  /// Extract unique dates from 5-day chart data
  static Map<String, List<int>> extract5DayDateLabelsWithPositions(List<KLineEntity> data) {
    if (data.isEmpty) return {};

    final datePositions = <String, List<int>>{};

    for (int i = 0; i < data.length; i++) {
      final entity = data[i];
      if (entity.time != null) {
        final date = DateTime.fromMillisecondsSinceEpoch(entity.time!, isUtc: true);
        final dateStr = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

        if (!datePositions.containsKey(dateStr)) {
          datePositions[dateStr] = [];
        }
        datePositions[dateStr]!.add(i);
      }
    }

    return datePositions;
  }

  /// Calculate accurate label positions based on actual data distribution
  static List<double> calculate5DayLabelPositions(List<KLineEntity> data, String marketType, double chartWidth) {
    if (data.isEmpty) return [];

    final datePositions = extract5DayDateLabelsWithPositions(data);
    final sortedDates = datePositions.keys.toList()..sort();
    final positions = <double>[];

    final totalDataPoints = data.length;

    for (final date in sortedDates) {
      final dayIndices = datePositions[date] ?? [];
      if (dayIndices.isNotEmpty) {
        // Calculate the center position of this day's actual data points
        final dayStartIndex = dayIndices.first;
        final dayEndIndex = dayIndices.last;
        final dayCenterIndex = (dayStartIndex + dayEndIndex) / 2;

        // Convert to chart position based on actual data distribution
        final relativePosition = dayCenterIndex / totalDataPoints;
        final chartPosition = relativePosition * chartWidth;

        positions.add(chartPosition);
      }
    }

    return positions;
  }

  /// Extract unique dates from 5-day chart data for date labels
  static List<String> extract5DayDateLabels(List<KLineEntity> data) {
    final datePositions = extract5DayDateLabelsWithPositions(data);
    return datePositions.keys.toList()..sort();
  }

  /// Update chart style with 5-day date labels if needed
  static ChartStyle update5DayLabels(ChartStyle chartStyle, ChartPeriodType periodType, List<KLineEntity> data,
      TodaysTab? marketTab, double chartWidth) {
    if (!periodType.isFiveDay || data.isEmpty || marketTab == null) return chartStyle;

    final dateLabels = extract5DayDateLabels(data);
    final marketType = getMarketTypeString(marketTab);
    final labelPositions = calculate5DayLabelPositions(data, marketType, chartWidth);

    return chartStyle.copyWith(
      custom5DayDateLabels: dateLabels,
      custom5DayLabelPositions: labelPositions,
      is5DayChart: true,
      marketType: marketType,
    );
  }
}
