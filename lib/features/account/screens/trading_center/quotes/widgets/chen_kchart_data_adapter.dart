import 'package:flutter_chen_kchart/k_chart.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_kline_data.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

/// Data adapter for converting our KlineItem data to flutter_chen_kchart format
/// This preserves all existing functionality while using the new chart package
class ChenKChartDataAdapter {
  /// Convert KlineItem list to KLineEntity list for flutter_chen_kchart
  static List<KLineEntity> convertKlineData(
    List<KlineItem>? klineItems, {
    required bool isLine,
    double? originalClosePrice,
  }) {
    if (klineItems == null || klineItems.isEmpty) return [];

    List<KLineEntity> entities = [];
    double? previousClose;

    for (int i = 0; i < klineItems.length; i++) {
      final item = klineItems[i];

      // Skip invalid items
      if (item.time == null || item.time! <= 0) continue;

      // Skip empty/invalid candlestick data (when API returns data:{})
      // But allow real-time data that has price but no OHLC
      if (!isLine &&
          ((item.open ?? 0) == 0 &&
              (item.close ?? 0) == 0 &&
              (item.high ?? 0) == 0 &&
              (item.low ?? 0) == 0 &&
              (item.volume ?? 0) == 0 &&
              (item.price ?? 0) == 0)) {
        continue;
      }

      // For line charts, skip if price is null or zero
      if (isLine && (item.price == null || item.price! <= 0)) {
        continue;
      }

      double openPrice;
      double closePrice;
      double highPrice;
      double lowPrice;

      if (isLine) {
        openPrice = item.price ?? 0;
        closePrice = item.price ?? 0;
        highPrice = item.price ?? 0;
        lowPrice = item.price ?? 0;
      } else {
        // For candlestick data
        if (item.open != null && item.open! > 0) {
          // Historical data with proper OHLC
          openPrice = item.open!;
          closePrice = item.close ?? item.price ?? 0;
          highPrice = item.high ?? item.price ?? 0;
          lowPrice = item.low ?? item.price ?? 0;
        } else if (item.price != null && item.price! > 0) {
          // Real-time data with only price - use previous close as open
          openPrice = previousClose ?? item.price!;
          closePrice = item.price!;
          highPrice = item.price!;
          lowPrice = item.price!;
        } else {
          // Fallback
          openPrice = previousClose ?? item.close ?? 0;
          closePrice = item.close ?? 0;
          highPrice = item.high ?? item.close ?? 0;
          lowPrice = item.low ?? item.close ?? 0;
        }
      }

      // API returns UTC timestamps, but we display them in chinese market time
      final marketOffset = 8; // Shanghai UTC+8
      final adjustedTime = ((item.time! + (marketOffset * 3600)) * 1000).toInt();

      final entity = KLineEntity.fromCustom(
        time: adjustedTime,
        close: closePrice,
        open: openPrice,
        high: highPrice,
        low: lowPrice,
        vol: item.volume ?? 0,
        amount: item.price ?? 0,
      );

      // Calculate change and ratio for each data point
      double? change;
      double? ratio;

      if (isLine) {
        // For real-time charts: use original close price
        if (originalClosePrice != null && originalClosePrice != 0) {
          change = closePrice - originalClosePrice;
          ratio = (change / originalClosePrice) * 100;
        }
      } else {
        // For K-line charts: use previous data point price
        if (previousClose != null && previousClose != 0) {
          change = closePrice - previousClose;
          ratio = (change / previousClose) * 100;
        }
      }

      if (change != null) entity.change = change;
      if (ratio != null) entity.ratio = ratio;

      entities.add(entity);
      previousClose = closePrice;
    }

    // Calculate technical indicators using the chart's built-in DataUtil
    if (entities.isNotEmpty) {
      try {
        DataUtil.calculate(entities, [5, 10, 20, 30, 60]);
      } catch (e) {
        // Handle calculation errors gracefully
      }
    }

    return entities;
  }

  /// Format large numbers with locale-aware units
  static String formatLargeNumber(double value, String locale) {
    if (locale.startsWith('zh')) {
      // Chinese formatting: 万, 亿
      if (value >= 100000000) {
        return '${(value / 100000000).toStringAsFixed(2)}亿';
      } else if (value >= 10000) {
        return '${(value / 10000).toStringAsFixed(2)}万';
      } else {
        return value.toStringAsFixed(2);
      }
    } else {
      // English formatting: K, M, B, T
      if (value >= 1000000000000) {
        return '${(value / 1000000000000).toStringAsFixed(2)}T';
      } else if (value >= 1000000000) {
        return '${(value / 1000000000).toStringAsFixed(2)}B';
      } else if (value >= 1000000) {
        return '${(value / 1000000).toStringAsFixed(2)}M';
      } else if (value >= 1000) {
        return '${(value / 1000).toStringAsFixed(2)}K';
      } else {
        return value.toStringAsFixed(2);
      }
    }
  }

  /// Get appropriate time format based on chart type
  static List<String> getTimeFormat(bool isIntraday, bool isLine) {
    if (isIntraday || isLine) {
      // For intraday charts, only show time (HH:mm) without date
      return [HH, ':', nn];
    } else {
      return TimeFormat.YEAR_MONTH_DAY;
    }
  }

  /// Get MA day list based on chart type
  static List<int> getMADayList(bool isIntraday) {
    if (isIntraday) {
      return [5, 10, 20]; // Fewer MA lines for intraday
    } else {
      return [5, 10, 20, 30, 60]; // Full MA lines for daily/weekly/monthly
    }
  }

  /// Extract market type from instrument string
  static TodaysTab getMarketFromInstrument(String instrument) {
    final parts = instrument.split('|');
    if (parts.isEmpty) return TodaysTab.aShares;

    final market = parts[0];
    return TodaysTab.byMarket(market);
  }
}
