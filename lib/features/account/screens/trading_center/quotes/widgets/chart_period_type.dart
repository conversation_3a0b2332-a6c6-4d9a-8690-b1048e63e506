
enum ChartPeriodType {
  /// Intraday chart - minute-by-minute data for current trading day
  intraday('intraday'),

  /// 5-day chart - minute-by-minute data for 5 trading days
  fiveDay('5day'),

  /// Daily K-line chart - daily candlestick data
  daily('daily-kline'),

  /// Weekly K-line chart - weekly candlestick data
  weekly('weekly-kline'),

  /// Monthly K-line chart - monthly candlestick data
  monthly('monthly-kline'),

  /// Yearly K-line chart - yearly candlestick data
  yearly('yearly-kline'),

  /// 1-minute K-line chart
  oneMinute('1min-kline'),

  /// 5-minute K-line chart
  fiveMinute('5min-kline'),

  /// 15-minute K-line chart
  fifteenMinute('15min-kline'),

  /// 30-minute K-line chart
  thirtyMinute('30min-kline');

  const ChartPeriodType(this.id);

  /// The ID that matches the KlineOption.id values
  final String id;

  /// Create ChartPeriodType from KlineOption ID
  static ChartPeriodType fromId(String id) {
    return ChartPeriodType.values.firstWhere(
      (type) => type.id == id,
      orElse: () => ChartPeriodType.intraday,
    );
  }

  /// Check if this is an intraday-type chart (minute-by-minute data)
  bool get isIntradayType {
    return this == ChartPeriodType.intraday || this == ChartPeriodType.fiveDay;
  }

  /// Check if this is a 5-day chart specifically
  bool get isFiveDay {
    return this == ChartPeriodType.fiveDay;
  }

  /// Check if this is a traditional intraday chart (single day)
  bool get isIntraday {
    return this == ChartPeriodType.intraday;
  }
}
