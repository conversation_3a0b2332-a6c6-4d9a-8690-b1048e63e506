import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2/widgets/stock_order_book/stock_order_book_section.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/broker_queue_view.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/kline_section.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/quotes_company_info.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/quotes_dist_section.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/quotes_news_list.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/stock_info.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/quotes/widgets/wish_button.dart';
import 'package:gp_stock_app/features/main/domain/enums/navigation_item.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/features/market/domain/models/stock_reponse/stock_info_response.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/features/market/logic/market/market_cubit.dart';
import 'package:gp_stock_app/features/market/warning/logic/warning_cubit.dart';
import 'package:gp_stock_app/features/market/warning/screens/add_warning_screen.dart';
import 'package:gp_stock_app/features/market/watch_list/logic/watch_list_cubit.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';

import '../../../../../shared/constants/assets.dart';
import '../../../../../shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/tab/common_tab_bar.dart';

class QuotesView extends StatefulWidget {
  const QuotesView({
    super.key,
    required this.instrument,
    required this.marketCategory,
    required this.isIndexTrading,
  });

  final Instrument instrument;
  final MarketCategory marketCategory;
  final bool isIndexTrading;

  @override
  State<QuotesView> createState() => _QuotesViewState();
}

class _QuotesViewState extends State<QuotesView> {
  List<TabType> _tabs = TabType.values;
  int _selectedTabIndex = 0;

  @override
  void initState() {
    super.initState();
    if (widget.isIndexTrading) setState(() => _tabs = TabType.indexTradingTabs);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      context.read<TradingCubit>().setBottomWidgetVisible(_selectedTabIndex == 0);
      context.read<WatchListCubit>().getWatchListByInstrument(
          '${widget.instrument.market}|${widget.instrument.securityType}|${widget.instrument.symbol}');
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tabData = _tabs.map((tab) => tab.name.tr()).toList();

    return Stack(
      children: [
        Column(
          children: [
            Container(
              height: 34,
              decoration: BoxDecoration(color: context.theme.scaffoldBackgroundColor),
              child: CommonTabBar.withAutoKey(
                tabData,
                currentIndex: _selectedTabIndex,
                onTap: (index) {
                  setState(() => _selectedTabIndex = index);
                  context.read<TradingCubit>().setBottomWidgetVisible(index == 0);
                },
                style: CommonTabBarStyle.line,
                isScrollable: false,
              ),
            ),
            StockInfoHeader(),
            Expanded(
              child: IndexedStack(
                index: _selectedTabIndex,
                children: [
                  SingleChildScrollView(
                    child: Column(
                      spacing: 14,
                      children: [
                        StockInfoBody(isIndexTrading: widget.isIndexTrading),
                        KlineSection(instrument: widget.instrument),
                        if (!widget.isIndexTrading) ...[
                          Container(
                            color: context.theme.cardColor,
                            padding: const EdgeInsets.all(20),
                            child:
                            StockOrderBookSection(marketCategory: widget.marketCategory),

                          ),
                          BrokerQueueView(),
                        ]
                      ],
                    ),
                  ),
                  if (widget.isIndexTrading) ...[
                    QuotesNewsList(instrument: widget.instrument)
                  ] else ...[
                    QuotesDistSection(),
                    QuotesNewsList(instrument: widget.instrument),
                    QuotesCompanyInfo(),
                  ],
                ],
              ),
            ),
            Builder(
              builder: (context) {
                final isBottomWidgetVisible = context.watch<TradingCubit>().state.isBottomWidgetVisible;
                return isBottomWidgetVisible ? SizedBox(height: 70.gw) : const SizedBox.shrink();
              },
            ),
          ],
        ),
        Builder(
          builder: (context) {
            final isBottomWidgetVisible = context.watch<TradingCubit>().state.isBottomWidgetVisible;
            return isBottomWidgetVisible
                ? _BottomWidget(widget.instrument, widget.isIndexTrading)
                : const SizedBox.shrink();
          },
        ),
      ],
    );
  }
}

class _BottomWidget extends StatelessWidget {
  const _BottomWidget(this.instrument, this.isIndexTrading);

  final bool isIndexTrading;
  final Instrument instrument;
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        height: 70.gw,
        padding: EdgeInsets.only(bottom: 5.gw),
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          boxShadow: [
            BoxShadow(
              color: context.theme.shadowColor,
              blurRadius: 33,
              spreadRadius: 5,
              offset: Offset(10, 20),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            BlocSelector<TradingCubit, TradingState, ({DataStatus stockInfoStatus, StockInfoData? stockInfo})>(
              selector: (state) => (stockInfoStatus: state.stockInfoStatus, stockInfo: state.stockInfo),
              builder: (context, state) {
                return _dataCell(
                    icon: Assets.bellIcon,
                    text: 'alert'.tr(),
                    onTap: () {
                      if (state.stockInfoStatus == DataStatus.success && state.stockInfo != null) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => BlocProvider(
                              create: (context) => getIt<WarningCubit>(),
                              child: AddWarningScreen(
                                stockData: state.stockInfo!,
                              ),
                            ),
                          ),
                        );
                      }
                    },
                    isEnabled: false,
                    context: context);
              },
            ),
            WishButton(instrument: '${instrument.market}|${instrument.securityType}|${instrument.symbol}'),
            10.horizontalSpace,
            SizedBox(
              height: 38.gw,
              width: 140.gw,
              child: CommonButton(
                title: 'trade2'.tr(),
                borderColor: context.theme.primaryColor,
                onPressed: () {
                  if (isIndexTrading) {
                    context.read<MainCubit>().selectedNavigationItem(BottomNavType.trade);
                    context.read<MarketCubit>().updateMainHeaderTab(MarketSectionTab.stockIndex);
                    context.read<IndexTradeCubit>().updateSelectedIndex(
                          context
                              .read<IndexTradeCubit>()
                              .state
                              .indexes
                              .indexWhere((element) => element.instrument == instrument.instrument),
                          animate: true,
                        );
                    getIt<NavigatorService>().popToRoot();
                    return;
                  }
                  context.read<TradingCubit>().setTradeType(TradeTabType.values.first);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _dataCell({
    required String icon,
    String? selectedIcon,
    required String text,
    required VoidCallback onTap,
    required bool isEnabled,
    required BuildContext context,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            height: 30.gw,
            width: 30.gw,
            padding: EdgeInsets.all(6.gr),
            decoration: BoxDecoration(
              color: context.theme.cardColor,
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Colors.black.withNewOpacity(0.3)
                      : Color(0xFFD3D1D8).withNewOpacity(0.3),
                  blurRadius: 16,
                  spreadRadius: 0,
                  offset: Offset(4, 9),
                ),
              ],
            ),
            child: SvgPicture.asset(
              isEnabled ? selectedIcon ?? icon : icon,
            ),
          ),
          Text(text, style: context.textTheme.regular.fs12.copyWith(color: context.colorTheme.textSecondary)),
        ],
      ),
    );
  }
}
