import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/watchlist/watchlist_item_entity.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_domin/f_trade_state_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_depth_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_tick_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/subviews/f_trade_k_line_tool_bar.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/subviews/subviews.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/features/market/watch_list/logic/watch_list_cubit.dart';
import 'package:gp_stock_app/features/market/watch_list/logic/watch_list_state.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class FTradeKLineScrollView extends StatefulWidget {
  final CNFuturesMarketType type;
  final FTradeListItemModel data;
  final void Function(int) onChangeAllInfoScreenTitlesAction;
  const FTradeKLineScrollView({
    super.key,
    required this.type,
    required this.data,
    required this.onChangeAllInfoScreenTitlesAction,
  });

  @override
  State<FTradeKLineScrollView> createState() => _FTradeKLineScrollViewState();
}

class _FTradeKLineScrollViewState extends State<FTradeKLineScrollView> {
  late String instrument;

  @override
  void initState() {
    super.initState();

    instrument = widget.data.makeInstrument();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<FTradeKLineCubit>().fetchTradeState(
            market: widget.data.market,
            productCode: widget.data.productCode,
          );
      context.read<FTradeKLineCubit>().fetchFQuotationAllData(instrument: instrument);
      context.read<FTradeKLineCubit>().startPolling(allOrSub: true);
      context.read<WatchListCubit>().getWatchListByInstrument(instrument);
    });
  }

  @override
  Widget build(BuildContext context) {
    final double bottomSafeHeight = MediaQuery.of(context).viewPadding.bottom;

    return Stack(children: [
      Padding(
        padding: EdgeInsets.only(bottom: 60 + bottomSafeHeight),
        child: CustomScrollView(
          physics: ClampingScrollPhysics(),
          slivers: [
            SliverPersistentHeader(
              pinned: true,
              delegate: _SliverHeaderDelegate(
                minHeight: 80,
                maxHeight: 80,
                child: _buildHeaderTop(),
              ),
            ),
            SliverToBoxAdapter(child: _buildHeaderBottom()),
            SliverToBoxAdapter(child: _buildKline()),
            SliverToBoxAdapter(child: _buildBuyAndSellView()),
          ],
        ),
      ),
      Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: _buildToolBar(instrument),
      ),
    ]);
  }

  Widget _buildHeaderTop() {
    return BlocSelector<FTradeKLineCubit, FTradeKLineState, (FTradeInfoModel, FTradeStateModel?)>(
      selector: (state) => (state.fTradeInfoModel ?? widget.data.toFTradeInfoModel(), state.fTradeStateModel),
      builder: (context, models) {
        return FTradeKLineHeaderTopView(data: models.$1, stateData: models.$2);
      },
    );
  }

  Widget _buildHeaderBottom() {
    return BlocSelector<FTradeKLineCubit, FTradeKLineState, FTradeInfoModel>(
      selector: (state) => state.fTradeInfoModel ?? widget.data.toFTradeInfoModel(),
      builder: (context, fTradeInfoModel) {
        return FTradeKLineHeaderBottomView(data: fTradeInfoModel);
      },
    );
  }

  Widget _buildKline() {
    // Map<String, FTradeKLineModel>
    // period=>1min/5min/15min/30min/60min/day/week/month/year
    return BlocSelector<
        FTradeKLineCubit,
        FTradeKLineState,
        (
          bool,
          String,
          DataStatus,
          Map<String, FTradeKLineModel>,
          FTradeTickModel,
          FTradeInfoModel?,
          FTradeStateModel?
        )>(
      selector: (state) => (
        state.isTimeOrKline,
        state.period,
        state.kLineDataStatus,
        state.kLineMap,
        state.fTradeTickModel,
        state.fTradeInfoModel,
        state.fTradeStateModel,
      ),
      builder: (context, newState) {
        return FTradeKLineView(
          instrument: instrument,
          isTimeOrKline: newState.$1,
          period: newState.$2,
          dataStatus: newState.$3,
          klineMap: newState.$4,
          fTradeTickModel: newState.$5,
          fTradeInfoModel: newState.$6,
          fTradeStateModel: newState.$7,
          onPeriodBarClick: (isTimeOrKline, period) {
            context.read<FTradeKLineCubit>().selectedPeriod(instrument, isTimeOrKline, period);
          },
          needLoadMoreTick: () {
            // 错误的需求,暂时移除
            // context.read<FTradeKLineCubit>().fetchMoreTicksData(instrument: instrument);
          },
        );
      },
    );
  }

  Widget _buildBuyAndSellView() {
    return BlocSelector<FTradeKLineCubit, FTradeKLineState, FTradeDepthModel?>(
      selector: (state) => state.fTradeDepthModel,
      builder: (context, fTradeDepthModel) {
        if (fTradeDepthModel == null) {
          return const SizedBox.shrink();
        }

        return FTradeKLineBuySellView(depthModel: fTradeDepthModel, needShowPaddingAndShadow: true,);
      },
    );
  }

  Widget _buildToolBar(String instrument) {
    return BlocSelector<WatchListCubit, WatchListState, (DataStatus, WatchlistItemEntity?)>(
      selector: (state) => (state.getWatchListByInstrumentStatus, state.watchListByInstrument),
      builder: (context, state) {
        final isWish = state.$2?.instrument == instrument;
        return FTradeKLineToolBar(
          isWish: isWish,
          instrument: instrument,
          onWarningBtnClick: () {
            GPEasyLoading.showToast('developing'.tr());
            // Navigator.push(
            //   context,
            //   MaterialPageRoute(
            //     builder: (context) => BlocProvider(
            //       create: (context) => getIt<WarningCubit>(),
            //       child: AddWarningScreen(
            //         stockData: state.stockInfo!,
            //       ),
            //     ),
            //   ),
            // );
          },
          onWishBtnClick: () async {
            final watchListCubit = context.read<WatchListCubit>();

            if (isWish) {
              watchListCubit.removeFromWatchList(state.$2!.id);
              return;
            }

            await watchListCubit.addToWatchList(
              market: widget.data.market,
              securityType: widget.data.securityType,
              symbol: widget.data.symbol,
            );

            if (!mounted) return;
            watchListCubit.getWatchListByInstrument(instrument);
          },
          onTradeBtnClick: () {
            widget.onChangeAllInfoScreenTitlesAction(0);
          },
        );
      },
    );
  }
}

class _SliverHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget child;

  _SliverHeaderDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
