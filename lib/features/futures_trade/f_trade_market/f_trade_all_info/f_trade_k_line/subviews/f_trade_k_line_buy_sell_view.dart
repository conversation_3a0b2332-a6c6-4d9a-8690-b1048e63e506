import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_depth_model.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2/widgets/stock_order_book/sub/bid_ask_order_size_widget.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2/widgets/stock_order_book/sub/bid_ask_ratio_bar.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/market_depth/market_depth.dart';

/// FIXME 后期优化的时候可以改成跟股票通用视图
class FTradeKLineBuySellView extends StatelessWidget {
  final bool needShowPaddingAndShadow;
  final FTradeDepthModel depthModel;
  final void Function(Ask)? onTapDepthOrder;
  const FTradeKLineBuySellView({
    super.key,
    required this.needShowPaddingAndShadow,
    required this.depthModel,
    this.onTapDepthOrder,
  });

  @override
  Widget build(BuildContext context) {
    final bidColor = context.colorTheme.stockRed;
    final askColor = context.colorTheme.stockGreen;
    return Container(
      padding: needShowPaddingAndShadow ? EdgeInsets.symmetric(vertical: 12, horizontal: 8) : EdgeInsets.zero,
      margin: needShowPaddingAndShadow ? EdgeInsets.only(bottom: 10) : EdgeInsets.zero,
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: needShowPaddingAndShadow ? BorderRadius.circular(8) : BorderRadius.zero,
        boxShadow: needShowPaddingAndShadow
            ? [
                BoxShadow(
                  color: Color(0x14354677),
                  offset: Offset(0, 4),
                  blurRadius: 8,
                  spreadRadius: 0,
                ),
              ]
            : [],
      ),
      child: AnimationLimiter(
        child:
        Column(
          mainAxisSize: MainAxisSize.min,
          spacing: 12,
          children: [
            _buildBuySellHeader(context: context, depthModel: depthModel,bidColor: bidColor,
              askColor: askColor,),

            BidAskOrderSizeWidget(
              bidList: depthModel.bid.map((e) => e.toAsk()).toList(),
              askList: depthModel.ask.map((e) => e.toAsk()).toList(),
              levelType: StockOrderLevelType.one,
              bidColor: bidColor,
              askColor: askColor,
              marketCategory: MarketCategory.cnFutures,
              onTapDepthOrder: onTapDepthOrder,
            )
          ],
        ),
      ),
    );
  }

  Widget _buildBuySellHeader({
    required BuildContext context,
    required FTradeDepthModel depthModel,
    required Color bidColor,
    required Color askColor,
  }) {

    // 顶档挂量（为空/空列表则为 0）
    final double bidVol = switch (depthModel.bid) { [final top, ...] => (top.vol ?? 0).toDouble(), _ => 0.0 };
    final double askVol = switch (depthModel.ask) { [final top, ...] => (top.vol ?? 0).toDouble(), _ => 0.0 };
    final double total = bidVol + askVol;
    final bool hasTotal = total > 0;
    final double bidRatio = hasTotal ? (bidVol / total) : 0.0;
    final double askRatio = hasTotal ? (1 - bidRatio) : 0.0;

    return Column(
      spacing: 5.gw,
      children: [
        Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'ask'.tr(),
                  style: context.textTheme.regular.fs12.copyWith(color: context.colorTheme.stockGreen),
                ),
                Expanded(child: SizedBox()),
                Text(
                  'bid'.tr(),
                  style: context.textTheme.regular.fs12.copyWith(color: context.colorTheme.stockRed),
                ),
                Expanded(child: SizedBox()),
              ],
            ),
          ],
        ),

        Row(
          children: [
            _buildRatioText(context, value: askRatio, color: askColor, isLeft: true),
            Expanded(child: BidAskRatioBar(bidVol: bidVol, askVol: askVol, bidColor: bidColor, askColor: askColor)),
            _buildRatioText(context, value: bidRatio, color: bidColor, isLeft: false),
          ],
        )
      ],
    );
  }


  Widget _buildRatioText(BuildContext context, {required double value, required Color color, required bool isLeft}) {
    return Container(
      width: 48.gw,
      alignment: isLeft ? Alignment.centerLeft : Alignment.centerRight,
      child: Text(
        '${((value.isFinite ? value : 0.0) * 100).toStringAsFixed(2)}%',
        style: context.textTheme.primary.fs12.ffAkz.copyWith(color: color),
      ),
    );
  }
}

