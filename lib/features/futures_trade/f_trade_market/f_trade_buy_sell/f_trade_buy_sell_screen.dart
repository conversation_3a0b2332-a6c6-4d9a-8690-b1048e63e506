import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account_v2/0_home/account_screen_cubit_v2.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/f_trade_acct_screen.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_depth_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/logic/f_trade_k_line_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/logic/f_trade_buy_sell_cubit.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/subviews/f_trade_buy_sell_Dialog.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_buy_sell/subviews/subviews.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

class FTradeBuySellScreen extends StatefulWidget {
  final CNFuturesMarketType type;
  final FTradeListItemModel data;
  final void Function(int) onChangeAllInfoScreenTitlesAction;

  const FTradeBuySellScreen({
    super.key,
    required this.data,
    required this.onChangeAllInfoScreenTitlesAction,
    required this.type,
  });

  @override
  State<FTradeBuySellScreen> createState() => _FTradeBuySellScreenState();
}

class _FTradeBuySellScreenState extends State<FTradeBuySellScreen> {
  late String instrument;

  /// 交易价格
  late TextEditingController priceEditingController;

  /// 交易X手(股)
  late TextEditingController numberEditingController;

  @override
  void initState() {
    super.initState();
    priceEditingController = TextEditingController();
    numberEditingController = TextEditingController();

    instrument = widget.data.makeInstrument();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startAfterBuild();
    });
  }

  Future<void> _startAfterBuild() async {
    context.read<FTradeKLineCubit>().fetchTradeState(market: widget.data.market, productCode: widget.data.productCode);
    context.read<FTradeBuySellCubit>().fetchUsableCash();
    context.read<FTradeBuySellCubit>().startUsableCashPolling();
    context.read<FTradeKLineCubit>().fetchFQuotationSubData(instrument: instrument);
    context.read<FTradeKLineCubit>().startPolling(allOrSub: false);
    await context.read<FTradeBuySellCubit>().fetchConfigData(widget.data);
    if (!mounted) return;
  }

  @override
  void dispose() {
    priceEditingController.dispose();
    numberEditingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final double bottomPadding = MediaQuery.of(context).padding.bottom;

    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 14.gw, vertical: 8.gw),
      child: AnimationLimiter(
        child: Column(
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 600),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 12.gw),
                decoration: BoxDecoration(
                  color: context.theme.cardColor,
                  borderRadius: BorderRadius.circular(10.gr),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildTopHeader(),
                    8.verticalSpace,
                    _buildActionsView(),
                  ],
                ),
              ),
              16.verticalSpace,
              MultiBlocProvider(
                providers: [
                  BlocProvider(create: (context) => TradingCubit()),
                  BlocProvider(create: (context) => AccountScreenCubitV2()),
                ],
                child: FTradeAcctScreen(
                  type: widget.type,
                  data: widget.data,
                  onChangeAllInfoScreenTitlesAction: widget.onChangeAllInfoScreenTitlesAction,
                ),
              ),
              SizedBox(height: bottomPadding),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopHeader() {
    final cubit = context.read<FTradeBuySellCubit>();
    return BlocSelector<FTradeKLineCubit, FTradeKLineState, ({FTradeInfoModel infoModel, FTradeDepthModel? depthModel})>(
      selector: (state) => (infoModel: state.fTradeInfoModel ?? widget.data.toFTradeInfoModel(), depthModel: state.fTradeDepthModel),
      builder: (context, state) {
        return FTradeBuySellHeaderView(data: state.infoModel, type: widget.type, depthData: state.depthModel, onTapDepthOrder: (item) {
            cubit.handleUserInputControllerChanged(cubit.state.userInputController.copyWith(priceType: PriceType.limit, price: item.price));
        },);
      },
    );
  }

  // TextEditing -> Cubit -> Counting(3.5s) -> Listener -> HasFocus(Y/N) ->  TextEditing
  Widget _buildActionsView() {
    return BlocConsumer<FTradeBuySellCubit, FTradeBuySellState>(listener: (context, state) {
      final double price = state.userInputController.price;
      final bool priceHasFocus = state.userInputController.priceHasFocus;
      if (priceHasFocus == false) {
        priceEditingController.text = price.toStringAsFixed(3);
      }

      final double number = state.userInputController.number;
      final bool numberHasFocus = state.userInputController.numberHasFocus;
      if (numberHasFocus == false) {
        numberEditingController.text = number.toStringAsFixed(0);
      }
    }, builder: (BuildContext context, state) {
      return FTradeBuySellInputView(
        accountUsableCash: state.accountUsableCash,
        latestPrice: state.fTradeLatestPrice ?? 0.0,
        fTradeConfigModel: state.fTradeConfigModel,
        userInputState: state.userInputController,
        priceEditingController: priceEditingController,
        numberEditingController: numberEditingController,
        onUserInputControllerChanged: (oldInputCtrl) {
          context.read<FTradeBuySellCubit>().handleUserInputControllerChanged(oldInputCtrl);
        },
        buyActionsController: state.longActionsController.copyWith(
          onConfirmPressed: () async {
            FocusScope.of(context).requestFocus(FocusNode());
            await Future.delayed(Duration(milliseconds: 150));
            if (!context.mounted) return;
            if (state.userInputController.tradeDirection == TradeDirection.sell && state.longActionsController.selectedOptionIdx != 0) return;
            final confirmList = context.read<FTradeBuySellCubit>().makeConfirmList(true);
            final queryParameters = context.read<FTradeBuySellCubit>().makeQueryParameters('buy');

            showDialog(
              context: context,
              builder: (_) {
                return FTradeBuySellDialog(
                  confirmList: confirmList,
                  submit: () {
                    context.read<FTradeBuySellCubit>().createOrder(queryParameters);
                  },
                );
              },
            );
          },
          onOptionCellSelected: (idx) {
            context.read<FTradeBuySellCubit>().handleLongActionSelectedIdx(idx);
          },
        ),
        sellActionsController: state.shortActionsController.copyWith(
          onConfirmPressed: () async {
            FocusScope.of(context).requestFocus(FocusNode());
            await Future.delayed(Duration(milliseconds: 150));
            if (context.mounted == false) return;
            if (state.userInputController.tradeDirection == TradeDirection.sell && state.shortActionsController.selectedOptionIdx != 0) return;
            final confirmList = context.read<FTradeBuySellCubit>().makeConfirmList(false);
            final queryParameters = context.read<FTradeBuySellCubit>().makeQueryParameters('sell');
            showDialog(
              context: context,
              builder: (_) {
                return FTradeBuySellDialog(
                  confirmList: confirmList,
                  submit: () {
                    context.read<FTradeBuySellCubit>().createOrder(queryParameters);
                  },
                );
              },
            );
          },
          onOptionCellSelected: (idx) {
            context.read<FTradeBuySellCubit>().handleShortActionSelectedIdx(idx);
          },
        ),
      );
    });
  }
}
