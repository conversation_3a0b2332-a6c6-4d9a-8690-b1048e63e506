import 'package:flutter/material.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/utils/trading_utils.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_depth_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_info_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/subviews/subviews.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market_enum.dart';
import 'package:gp_stock_app/shared/models/market_depth/market_depth.dart';

import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

/// 头部信息和深度交易数据(买盘/卖盘)
class FTradeBuySellHeaderView extends StatelessWidget {
  final CNFuturesMarketType type;
  final FTradeInfoModel data;
  final FTradeDepthModel? depthData;
  final void Function(Ask)? onTapDepthOrder;
  const FTradeBuySellHeaderView({
    super.key,
    required this.data,
    required this.type,
    required this.depthData,
    required this.onTapDepthOrder,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildMarketStatus(context),
        const SizedBox(height: 10),
        _buildStockDetails(context),
        if (depthData != null)
          FTradeKLineBuySellView(
            depthModel: depthData!,
            needShowPaddingAndShadow: false,
            onTapDepthOrder: onTapDepthOrder,
          ),

      ],
    );
  }

  /// Builds the market status and timestamp row.
  Widget _buildMarketStatus(BuildContext context) {
    return Row(
      children: [
        Text(
          data.name,
          style: context.textTheme.primary,
        ),
        const SizedBox(width: 8),
        Container(
          height: 15.gw,
          padding: EdgeInsets.symmetric(horizontal: 4.gw, vertical: 2.gw),
          decoration: BoxDecoration(
            color: Color(0xffE9F0FD),
            borderRadius: BorderRadius.circular(2.gw),
          ),
          child: Text(
            type.cloudMarketCode,
            style: context.textTheme.primary.fs8,
          ),
        ),
      ],
    );
  }

  /// Builds the stock price, gain, and name section.
  Widget _buildStockDetails(BuildContext context) {
    final chg = TradingUtils.formatNumber(data.chg);
    final gain = TradingUtils.formatPercentage(data.gain * 100, isPercent: false, decimalPlaces: 3);
    final gainColor = data.gain.getValueColor(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 4,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  FlipText(
                    data.latestPrice,
                    style: context.textTheme.primary.fs20.w700.copyWith(color: gainColor),
                    fractionDigits: 3,
                  ),
                  Icon(
                    data.gain >= 0 ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                    color: gainColor,
                  ),
                ],
              ),
              Text(
                "${TradingUtils.getSign(data.gain)}$chg  ${TradingUtils.getSign(data.gain)}$gain %",
                style: context.textTheme.primary.fs13.w700.copyWith(color: gainColor),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
