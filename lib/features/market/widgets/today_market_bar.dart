import 'package:easy_localization/easy_localization.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/market/widgets/market_tooltip.dart';

import '../../../shared/widgets/card/section_container.dart';
import 'today_market_status.dart';

class TodayMarketBar extends StatefulWidget {
  const TodayMarketBar({
    super.key,
    required this.list,
    this.low,
    this.up,
    this.zero,
  });

  final List<int> list;
  final int? low;
  final int? up;
  final int? zero;

  @override
  State<TodayMarketBar> createState() => _TodayMarketBarState();
}

class _TodayMarketBarState extends State<TodayMarketBar> {
  @override
  Widget build(BuildContext context) {
    return SectionContainer(
      title: 'todays_stock_market'.tr(),
      showDivider: false,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: height,
            child: Stack(
              children: [
                BarChart(
                  BarChartData(
                    alignment: BarChartAlignment.spaceAround,
                    barGroups: _createBarGroups(widget.list),
                    // titlesData: FlTitlesData(topTitles: AxisTitles(sideTitles: SideTitles(
                    //   showTitles: true,
                    //   getTitlesWidget: (double value, TitleMeta meta) {
                    //     // final group = widget.list.firstWhere((e) => e.toDouble() == value);
                    //     // final toY = group.barRods.first.toY;
                    //     return Text(
                    //       group.toStringAsFixed(1),
                    //       style: TextStyle(fontSize: 12, color: Colors.black),
                    //     );
                    //   },
                    //   reservedSize: 28,
                    // ))),
                    titlesData: const FlTitlesData(show: false),
                    gridData: const FlGridData(show: false),
                    borderData: FlBorderData(show: false),
                    barTouchData: BarTouchData(enabled: false),
                  ),
                ),
                Positioned.fill(
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      final barGroups = _createBarGroups(widget.list);
                      final maxValue =
                          barGroups.map((group) => group.barRods.first.toY).reduce((a, b) => a > b ? a : b);
                      return Stack(
                        children: barGroups.asMap().entries.map((entry) {
                          final index = entry.key;
                          final group = entry.value;
                          final barHeight = (group.barRods.first.toY / maxValue) * height;
                          final value = (group.barRods.first.toY - variate).toInt();

                          return Positioned(
                            left: (constraints.maxWidth / barGroups.length) * index +
                                (constraints.maxWidth / barGroups.length / 2) -
                                (barWidth / 2),
                            bottom: (barHeight - 12),
                            child: Container(
                              width: barWidth,
                              decoration: BoxDecoration(
                                color: context.theme.scaffoldBackgroundColor,
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(8.gr),
                                  topRight: Radius.circular(8.gr),
                                ),
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                '$value',
                                style: context.textTheme.primary.fs8.w500,
                              ),
                            ),
                          );
                        }).toList(),
                      );
                    },
                  ),
                ),
                Positioned.fill(
                  child: LayoutBuilder(
                    builder: (context, constraints) {
                      final barGroups = _createBarGroups(widget.list);
                      final maxValue =
                          barGroups.map((group) => group.barRods.first.toY).reduce((a, b) => a > b ? a : b);
                      return Stack(
                        children: barGroups.asMap().entries.map((entry) {
                          final index = entry.key;
                          final group = entry.value;
                          final barHeight = (group.barRods.first.toY / maxValue) * height;
                          return Positioned(
                            left: (constraints.maxWidth / barGroups.length) * index +
                                (constraints.maxWidth / barGroups.length / 2) -
                                (barWidth / 2),
                            bottom: 0,
                            child: InkWell(
                              onTap: () => _showTooltip(context, index, group, barHeight, constraints, barGroups),
                              child: Container(
                                color: Colors.transparent,
                                width: barWidth,
                                height: barHeight,
                              ),
                            ),
                          );
                        }).toList(),
                      );
                    },
                  ),
                )
              ],
            ),
          ),
          // Bottom Labels
          Padding(
            padding: const EdgeInsets.fromLTRB(0, 4, 0, 0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: labels
                  .map(
                    (label) => SizedBox(
                      width: barWidth,
                      child: Text(
                        label,
                        textAlign: TextAlign.center,
                        style: context.textTheme.regular.w500.copyWith(
                          fontSize: 7.gsp,
                        ),
                      ),
                    ),
                  )
                  .toList(),
            ),
          ),
          TodayMarketStatus(
            upCount: widget.up,
            flatCount: widget.zero,
            downCount: widget.low,
          ),
        ],
      ),
    );
  }
}

List<BarChartGroupData> _createBarGroups(List list) {
  if (list.isEmpty) {
    return [];
  }
  return List.generate(
    list.length,
    (index) => BarChartGroupData(
      x: index,
      barRods: [
        BarChartRodData(
          toY: list[index].toDouble() + variate,
          color: colors[index],
          width: barWidth,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(8.gr),
            topRight: Radius.circular(8.gr),
          ),
        ),
      ],
      barsSpace: 0,
    ),
  );
}

final labels = [
  '≥10%',
  '10~7%',
  '7~5%',
  '5~2%',
  '2~0%',
  '0',
  '0~2%',
  '2~5%',
  '5~7%',
  '7~10%',
  '≤10%',
];
final colors = [
  Color(0xFF2cb274),
  Color(0xFF2cb274),
  Color(0xFF2cb274),
  Color(0xFF2cb274),
  Color(0xFF2cb274),
  Color(0xffb4b8c9),
  Color(0xffc92c31),
  Color(0xffc92c31),
  Color(0xffc92c31),
  Color(0xffc92c31),
  Color(0xffc92c31),
].toList();
const height = 165.0;
const variate = 300.0;
final barWidth = 0.075.gsw;

void _showTooltip(BuildContext context, int index, BarChartGroupData group, double barHeight,
    BoxConstraints constraints, List<BarChartGroupData> barGroups) {
  final RenderBox renderBox = context.findRenderObject() as RenderBox;
  final position = renderBox.localToGlobal(Offset.zero);
  final barPosition = position.translate(
      (constraints.maxWidth / barGroups.length) * index + (constraints.maxWidth / barGroups.length / 2), -barHeight);

  showDialog(
    context: context,
    barrierColor: Colors.transparent,
    builder: (context) => Stack(
      children: [
        Positioned(
          left: barPosition.dx - (index == 0 ? 20 : 30),
          top: barPosition.dy + 50,
          child: Material(
            color: Colors.transparent,
            child: MarketTooltip(
                label: labels[index], value: (group.barRods.first.toY - variate).toInt(), color: colors[index]),
          ),
        ),
      ],
    ),
  );
}
