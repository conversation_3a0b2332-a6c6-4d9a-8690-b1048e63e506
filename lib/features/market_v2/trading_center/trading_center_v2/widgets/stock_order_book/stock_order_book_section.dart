import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2/widgets/stock_order_book/sub/bid_ask_order_size_widget.dart';
import 'package:gp_stock_app/features/market_v2/trading_center/trading_center_v2/widgets/stock_order_book/sub/bid_ask_ratio_bar.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/market_depth/market_depth.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

/// 股票订单簿展示区（BBO/深度）
///
/// - 顶部：卖/买标题与档位选择（A/H 市场支持 1/5 档，美股仅 1 档）；
/// - 中部：买卖量比例条（基于顶档 bid/ask 挂量）；
/// - 底部：左右列挂单（按所选档位展示）；
///
/// 参考：`https://support.futunn.com/topic479` `https://support.futunn.com/en/topic479`
class StockOrderBookSection extends StatelessWidget {
  const StockOrderBookSection({
    super.key,
    required this.marketCategory,
    this.onTapAsk,
  });

  /// 股票市场类型：
  /// 这里A股和港股有1、5档，其他只有1档数据
  final MarketCategory marketCategory;

  /// 点击档位回调
  final void Function(Ask)? onTapAsk;

  @override
  Widget build(BuildContext context) {
    final bidColor = context.colorTheme.stockRed;
    final askColor = context.colorTheme.stockGreen;

    return Column(
      children: [
        _buildBidAskRatioWidget(context, bidColor: bidColor, askColor: askColor),
        SizedBox(height: 10.gw),
        _buildBidAskOrderSizeWidget(bidColor: bidColor, askColor: askColor),
      ],
    );
  }

  /// 顶部买卖比例与档位选择
  Widget _buildBidAskRatioWidget(BuildContext context, {required Color bidColor, required Color askColor}) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('ask'.tr(), style: context.textTheme.regular.fs12),
            Text('bid'.tr(), style: context.textTheme.regular.fs12),
            PopupMenuButton<StockOrderLevelType>(
              constraints: BoxConstraints.expand(width: 40.gw, height: 90.gw),
              padding: EdgeInsets.zero,
              initialValue: context.read<TradingCubit>().state.stockWidgetCount,
              color: context.theme.cardColor,
              onSelected: (StockOrderLevelType item) {
                context.read<TradingCubit>().setStockWidgetCount(item);
              },
              child: BlocSelector<TradingCubit, TradingState, StockOrderLevelType>(
                selector: (state) => state.stockWidgetCount,
                builder: (context, stockWidgetCount) {
                  return _buildLevelMenuItem(context, value: stockWidgetCount.value);
                },
              ),
              itemBuilder: (BuildContext context) =>
                  (marketCategory.code < 3 ? StockOrderLevelType.values : [StockOrderLevelType.one])
                      .map((e) => PopupMenuItem<StockOrderLevelType>(
                            value: e,
                            child: _buildLevelMenuItem(context, value: e.value),
                          ))
                      .toList(),
            ),
          ],
        ),
        SizedBox(height: 5.gw),
        BlocSelector<TradingCubit, TradingState, MarketDepthData?>(
          selector: (state) => state.marketDepth,
          builder: (context, marketDepth) {
            if (marketDepth == null) return const SizedBox.shrink();

            // 顶档挂量（为空/空列表则为 0）
            final double bidVol = switch (marketDepth.bid) { [final top, ...] => (top.vol ?? 0).toDouble(), _ => 0.0 };
            final double askVol = switch (marketDepth.ask) { [final top, ...] => (top.vol ?? 0).toDouble(), _ => 0.0 };
            final double total = bidVol + askVol;
            final bool hasTotal = total > 0;
            final double bidRatio = hasTotal ? (bidVol / total) : 0.0;
            final double askRatio = hasTotal ? (1 - bidRatio) : 0.0;
            return Row(
              children: [
                _buildRatioText(context, value: askRatio, color: askColor, isLeft: true),
                Expanded(child: BidAskRatioBar(bidVol: bidVol, askVol: askVol, bidColor: bidColor, askColor: askColor)),
                _buildRatioText(context, value: bidRatio, color: bidColor, isLeft: false),
              ],
            );
          },
        )
      ],
    );
  }

  Widget _buildLevelMenuItem(BuildContext context, {required int value}) {
    return Container(
      width: 18.gw,
      height: 18.gw,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4.gr),
        color: context.theme.cardColor,
        border: Border.all(width: .3, color: context.colorTheme.textPrimary),
      ),
      alignment: Alignment.center,
      child: Text(
        value.toString(),
        style: context.textTheme.regular.fs12.w600.ffAkz,
      ),
    );
  }

  Widget _buildRatioText(BuildContext context, {required double value, required Color color, required bool isLeft}) {
    return Container(
      width: 48.gw,
      alignment: isLeft ? Alignment.centerLeft : Alignment.centerRight,
      child: Text(
        '${((value.isFinite ? value : 0.0) * 100).toStringAsFixed(2)}%',
        style: context.textTheme.primary.fs12.ffAkz.copyWith(color: color),
      ),
    );
  }

  Widget _buildBidAskOrderSizeWidget({required Color bidColor, required Color askColor}) {
    return BlocSelector<TradingCubit, TradingState,
            ({DataStatus marketDepthSatus, MarketDepthData? marketDepth, StockOrderLevelType levelType})>(
        selector: (state) => (
              marketDepthSatus: state.marketDepthStatus,
              marketDepth: state.marketDepth,
              levelType: state.stockWidgetCount,
            ),
        builder: (context, state) {
          if (state.marketDepthSatus == DataStatus.loading) {
            return ShimmerWidget(height: 80.gw);
          }
          if (state.marketDepthSatus == DataStatus.failed) {
            return const SizedBox.shrink();
          }

          return BidAskOrderSizeWidget(
            bidList: state.marketDepth?.bid ?? [],
            askList: state.marketDepth?.ask ?? [],
            levelType: state.levelType,
            bidColor: bidColor,
            askColor: askColor,
            marketCategory: marketCategory,
            onTapDepthOrder: onTapAsk,
          );
        });
  }
}
