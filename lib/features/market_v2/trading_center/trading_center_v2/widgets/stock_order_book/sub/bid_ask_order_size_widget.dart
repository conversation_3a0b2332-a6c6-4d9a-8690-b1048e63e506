import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import '../../../../../../../shared/models/market_depth/market_depth.dart';

/// 1/5档挂单量对比控件
///
/// - 左列卖盘、右列买盘；
/// - 每行展示：档位序号、价格、挂量（含笔数）；
/// - 固定 1/5 条目时使用 Column 生成，避免 shrinkWrap ListView 的测量开销；
/// - 根据市场类型格式化数量单位（例如 A 股 1 手=100 股）。
class BidAskOrderSizeWidget extends StatelessWidget {
  final List<Ask> bidList;
  final List<Ask> askList;
  final StockOrderLevelType levelType;
  final Color bidColor;
  final Color askColor;
  final MarketCategory marketCategory;
  final void Function(Ask)? onTapDepthOrder;

  const BidAskOrderSizeWidget({
    super.key,
    required this.bidList,
    required this.askList,
    required this.levelType,
    required this.bidColor,
    required this.askColor,
    required this.marketCategory,
    this.onTapDepthOrder,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(child: _buildOrderList(context, dataList: askList, isBid: false)),
        Expanded(child: _buildOrderList(context, dataList: bidList, isBid: true)),
      ],
    );
  }

  Widget _buildOrderList(
    BuildContext context, {
    required List<Ask> dataList,
    required bool isBid,
  }) {
    final int itemCount = min(levelType.value, dataList.length);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(
        itemCount,
        (index) => _buildOrderCell(context, item: dataList[index], index: index, isBid: isBid),
      ),
    );
  }

  Widget _buildOrderCell(
    BuildContext context, {
    required Ask item,
    required int index,
    required bool isBid,
  }) {
    final textColor = isBid ? bidColor : askColor;
    final subText = marketCategory == MarketCategory.cnFutures ? "" : "(${(item.no ?? '-').toString()})";
    return Material(
      color: Colors.transparent, // 保持背景透明
      child: InkWell(
        highlightColor: textColor.withValues(alpha: 0.5),
        splashColor: textColor.withValues(alpha: 0.5),
        onTap: onTapDepthOrder != null ? () {
          HapticFeedback.selectionClick(); // 轻微震动
          onTapDepthOrder?.call(item);
        } : null,
        child: Container(
          height: 30.gw,
          color: textColor.withValues(alpha: 0.1),
          padding: EdgeInsets.symmetric(horizontal: 8.gw),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                height: 18.gw,
                width: 18.gw,
                decoration: BoxDecoration(
                  color: textColor,
                  borderRadius: BorderRadius.circular(2.gr),
                ),
                child: Center(
                  child: Text(
                    item.depthNo.toString(),
                    style: context.textTheme.primary.fs12.w600.ffAkz.copyWith(
                      color: context.theme.cardColor,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 2.gw),
              Expanded(
                flex: 2,
                child: Align(
                  alignment: Alignment.centerRight,
                  child: Text(
                    _formatPrice(item.price),
                    style: context.textTheme.primary.fs12.w600.ffAkz.copyWith(
                      color: textColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Align(
                  alignment: Alignment.centerRight,
                  child: Text(
                    '${_formatVolume(item.vol)}$subText',
                    style: context.textTheme.primary.fs12.w600.ffAkz,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 根据市场规则格式化价格（占位：当前仅统一小数位到 3 位，可按精度接入）
  String _formatPrice(double? price) {
    final p = price ?? 0.0;
    return p.toStringAsFixed(3);
  }

  /// 根据市场规则格式化数量
  /// - CN: 以手为单位（1 手 = 100 股），展示“万/亿”单位
  /// - HK/US: 暂以股为单位直接换算中文单位
  String _formatVolume(int? vol) {

    final int raw = vol ?? 0;
    return switch (marketCategory) {
      MarketCategory.cnStocks => formatNumberWithChineseUnits(raw.toDouble() / 100.0),
      _ => formatNumberWithChineseUnits(raw.toDouble()),
    };
  }
}
