import 'package:bloc/bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/alipay.dart';
import 'package:gp_stock_app/core/models/entities/alipay_account_entity.dart';
import 'package:gp_stock_app/features/account/domain/repository/bank_repository.dart';
import 'package:gp_stock_app/features/profile/domain/repository/third_party_channel_repository.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'alipay_deposit_state.dart';

class AlipayDepositCubit extends Cubit<AlipayDepositState> {
  AlipayDepositCubit() : super(const AlipayDepositState()) {
    _initializeDepositConfig();
    _fetchAliPayList();
  }

  /// Initialize deposit configuration
  Future<void> _initializeDepositConfig() async {
    emit(state.copyWith(depositConfigStatus: DataStatus.loading));
    try {
      final config = await AlipayApi.fetchAlipayWithdrawConfig(); // Reusing withdraw config for now
      emit(state.copyWith(depositConfigEntity: config, depositConfigStatus: DataStatus.success));
    } catch (e) {
      emit(state.copyWith(depositConfigStatus: DataStatus.failed));
    }
  }

  /// Fetch Alipay accounts
  Future<void> _fetchAliPayList() async {
    emit(state.copyWith(alipayAccountStatus: DataStatus.loading));
    try {
      final alipayAccounts = await AlipayApi.listAlipayAccount();
      emit(state.copyWith(alipayAccounts: alipayAccounts, alipayAccountStatus: DataStatus.success));
    } catch (e) {
      emit(state.copyWith(alipayAccountStatus: DataStatus.failed));
    }
  }

  void updateSelectedAlipayAccount(AlipayAccountEntity account) {
    emit(state.copyWith(selectedAlipayAccount: account));
  }

  /// Update deposit amount
  void updateDepositAmount(String amount) {
    final isValid = _validateAmount(amount);
    emit(state.copyWith(
      depositAmount: () => int.tryParse(amount) ?? 0,
      isAmountValid: isValid,
    ));
  }

  /// Validate deposit amount
  bool _validateAmount(String amount) {
    if (amount.trim().isEmpty) return true;

    final double? parsedAmount = double.tryParse(amount);
    if (parsedAmount == null) return false;

    if (state.depositConfigEntity?.minWithdrawalAmount != null &&
        parsedAmount < state.depositConfigEntity!.minWithdrawalAmount) {
      return false;
    }
    if (state.depositConfigEntity?.maxWithdrawalAmount != null &&
        parsedAmount > state.depositConfigEntity!.maxWithdrawalAmount) {
      return false;
    }

    return true;
  }

  /// Submit deposit request
  Future<void> submitDeposit() async {
    if (!state.canSubmit) return;

    emit(state.copyWith(submitStatus: DataStatus.loading));

    try {
      // Using the pay function from BankRepository
      final response = await getIt<BankRepository>().pay(
        payAmount: state.depositAmount!,
        sysBankCardId: state.selectedAlipayAccount!.id,
        type: 5,
        orderNumber: state.orderNumber,
      );

      emit(state.copyWith(
        submitStatus: response.data == true ? DataStatus.success : DataStatus.failed,
        error: () => response.error,
      ));
    } catch (e) {
      emit(state.copyWith(
        submitStatus: DataStatus.failed,
        error: () => e.toString(),
      ));
    }
  }

  /// Reset form
  void resetForm() {
    emit(state.copyWith(
      depositAmount: () => null,
      submitStatus: DataStatus.idle,
      isAmountValid: true,
      error: () => null,
    ));
  }

  Future<void> bindUserWallet({
    required String payAddress,
  }) async {
    emit(state.copyWith(bindWalletStatus: DataStatus.loading));

    try {
      final result = await getIt<ThirdPartyChannelRepository>().bindUserWallet(
        bankCode: 'ALIPAY',
        payAddress: payAddress,
      );

      if (result.isSuccess) {
        emit(state.copyWith(
          bindWalletStatus: DataStatus.success,
          error: () => null,
        ));
        await _fetchAliPayList();
      } else {
        emit(state.copyWith(
          bindWalletStatus: DataStatus.failed,
          error: () => result.error,
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        bindWalletStatus: DataStatus.failed,
        error: () => e.toString(),
      ));
    }
  }

  void updateOrderNumber(String? orderNumber) {
    emit(state.copyWith(orderNumber: orderNumber));
  }
}