import 'package:easy_localization/easy_localization.dart';

class Validators {
  static final RegExp _mobileRegex = RegExp(r'^1[3-9]\d{9}$');
  static final RegExp isAuthName = RegExp(r'^[\p{L} ]+$', unicode: true);
  static final RegExp isIdCard = RegExp(r'^\d{17}[\dXx]$');
  static final RegExp isPassport = RegExp(r'^([DEGHMPS]{1,2})\d{7,8}$');
  static final RegExp isPassword = RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,16}$');
  static final RegExp isBankCard = RegExp(r'^\d{10,19}$');
  static final newPassword = RegExp(r'^.{6,16}$');
  static final inviteCode = RegExp(r'^.{5,11}$');

  static bool isMobileValid(String mobile) {
    return _mobileRegex.hasMatch(mobile);
  }

  static bool isBankCardValid(String bankCard) {
    return isBankCard.hasMatch(bankCard);
  }

  static String? validateMobile(
    String? mobile, {
    String? oldMobile,
  }) {
    if (mobile == oldMobile) return 'newMobileCannotBeSameAsOld'.tr();
    if (mobile == null || mobile.isEmpty) {
      return 'loginPhone'.tr();
    }
    if (!isMobileValid(mobile)) {
      return 'invalidMobileNumber'.tr();
    }
    return null;
  }

  //create password validation 8-16 containing upper and lower case letters and numbers
  static String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'passwordRequired'.tr();
    }
    if (!newPassword.hasMatch(password)) {
      return 'registerPasswordHint'.tr();
    }
    return null;
  }

  static String? validateVerificationCode(String? verificationCode) {
    if (verificationCode == null || verificationCode.isEmpty) {
      return 'verificationCodeRequired'.tr();
    }
    return null;
  }
}
