import 'package:gp_stock_app/shared/constants/enums.dart';

/// 总计分钟数 包含收尾需+1

/// 🇨🇳A 股分时图：共 240 分钟
///
/// - 上午：9:30 - 11:30（2 小时 = 120 分钟）
/// - 下午：13:00 - 15:00（2 小时 = 120 分钟）
/// - 午休：11:30 - 13:00（不交易）
/// - 总计：120 + 120 = 240 分钟
const int kAShareMinutesPerDay = 241;

/// 🇭🇰港股分时图：共 330 分钟
///
/// - 上午：9:30 - 12:00（2.5 小时 = 150 分钟）
/// - 下午：13:00 - 16:00（3 小时 = 180 分钟）
/// - 午休：12:00 - 13:00
/// - 总计：150 + 180 = 330 分钟
const int kHKShareMinutesPerDay = 331;

/// 🇺🇸美股分时图（常规盘）：共 390 分钟
///
/// - 全天：9:30 - 16:00（6.5 小时 = 390 分钟）
/// - 无午休，连续交易
const int kUSShareMinutesPerDay = 390;

class TimeRange {
  static int shareMinutesPerDay(MainMarketType market) {
    switch (market) {
      case MainMarketType.cnShares:
        return kAShareMinutesPerDay;
      case MainMarketType.hkShares:
        return kHKShareMinutesPerDay;
      case MainMarketType.usShares:
        return kUSShareMinutesPerDay;
    }
  }
}
