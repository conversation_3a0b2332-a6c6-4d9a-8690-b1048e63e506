import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_all_info/f_trade_k_line/domain/f_trade_k_line_model.dart';
import 'package:k_chart_plus/entity/k_line_entity.dart';


FTradeKLineModel $FTradeKLineModelFromJson(Map<String, dynamic> json) {
  final FTradeKLineModel fTradeKLineModel = FTradeKLineModel();
  final FTradeInfoKLineModel? detail = jsonConvert.convert<
      FTradeInfoKLineModel>(json['detail']);
  if (detail != null) {
    fTradeKLineModel.detail = detail;
  }
  final List<FTradeKLineItem>? list = (json['list'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<FTradeKLineItem>(e) as FTradeKLineItem)
      .toList();
  if (list != null) {
    fTradeKLineModel.list = list;
  }
  return fTradeKLineModel;
}

Map<String, dynamic> $FTradeKLineModelToJson(FTradeKLineModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['detail'] = entity.detail.toJson();
  data['list'] = entity.list.map((v) => v.toJson()).toList();
  return data;
}

extension FTradeKLineModelExtension on FTradeKLineModel {
  FTradeKLineModel copyWith({
    FTradeInfoKLineModel? detail,
    List<FTradeKLineItem>? list,
  }) {
    return FTradeKLineModel()
      ..detail = detail ?? this.detail
      ..list = list ?? this.list;
  }
}

FTradeInfoKLineModel $FTradeInfoKLineModelFromJson(Map<String, dynamic> json) {
  final FTradeInfoKLineModel fTradeInfoKLineModel = FTradeInfoKLineModel();
  final String? symbol = jsonConvert.convert<String>(json['symbol']);
  if (symbol != null) {
    fTradeInfoKLineModel.symbol = symbol;
  }
  final String? market = jsonConvert.convert<String>(json['market']);
  if (market != null) {
    fTradeInfoKLineModel.market = market;
  }
  final String? securityType = jsonConvert.convert<String>(
      json['securityType']);
  if (securityType != null) {
    fTradeInfoKLineModel.securityType = securityType;
  }
  final double? latestPrice = jsonConvert.convert<double>(json['latestPrice']);
  if (latestPrice != null) {
    fTradeInfoKLineModel.latestPrice = latestPrice;
  }
  final double? close = jsonConvert.convert<double>(json['close']);
  if (close != null) {
    fTradeInfoKLineModel.close = close;
  }
  final double? open = jsonConvert.convert<double>(json['open']);
  if (open != null) {
    fTradeInfoKLineModel.open = open;
  }
  final double? high = jsonConvert.convert<double>(json['high']);
  if (high != null) {
    fTradeInfoKLineModel.high = high;
  }
  final double? low = jsonConvert.convert<double>(json['low']);
  if (low != null) {
    fTradeInfoKLineModel.low = low;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    fTradeInfoKLineModel.name = name;
  }
  return fTradeInfoKLineModel;
}

Map<String, dynamic> $FTradeInfoKLineModelToJson(FTradeInfoKLineModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['symbol'] = entity.symbol;
  data['market'] = entity.market;
  data['securityType'] = entity.securityType;
  data['latestPrice'] = entity.latestPrice;
  data['close'] = entity.close;
  data['open'] = entity.open;
  data['high'] = entity.high;
  data['low'] = entity.low;
  data['name'] = entity.name;
  return data;
}

extension FTradeInfoKLineModelExtension on FTradeInfoKLineModel {
  FTradeInfoKLineModel copyWith({
    String? symbol,
    String? market,
    String? securityType,
    double? latestPrice,
    double? close,
    double? open,
    double? high,
    double? low,
    String? name,
  }) {
    return FTradeInfoKLineModel()
      ..symbol = symbol ?? this.symbol
      ..market = market ?? this.market
      ..securityType = securityType ?? this.securityType
      ..latestPrice = latestPrice ?? this.latestPrice
      ..close = close ?? this.close
      ..open = open ?? this.open
      ..high = high ?? this.high
      ..low = low ?? this.low
      ..name = name ?? this.name;
  }
}

FTradeKLineItem $FTradeKLineItemFromJson(Map<String, dynamic> json) {
  final FTradeKLineItem fTradeKLineItem = FTradeKLineItem();
  final int? time = jsonConvert.convert<int>(json['time']);
  if (time != null) {
    fTradeKLineItem.time = time;
  }
  final double? price = jsonConvert.convert<double>(json['price']);
  if (price != null) {
    fTradeKLineItem.price = price;
  }
  final double? volume = jsonConvert.convert<double>(json['volume']);
  if (volume != null) {
    fTradeKLineItem.volume = volume;
  }
  final double? open = jsonConvert.convert<double>(json['open']);
  if (open != null) {
    fTradeKLineItem.open = open;
  }
  final double? close = jsonConvert.convert<double>(json['close']);
  if (close != null) {
    fTradeKLineItem.close = close;
  }
  final double? high = jsonConvert.convert<double>(json['high']);
  if (high != null) {
    fTradeKLineItem.high = high;
  }
  final double? low = jsonConvert.convert<double>(json['low']);
  if (low != null) {
    fTradeKLineItem.low = low;
  }
  final double? amount = jsonConvert.convert<double>(json['amount']);
  if (amount != null) {
    fTradeKLineItem.amount = amount;
  }
  final double? avgPrice = jsonConvert.convert<double>(json['avgPrice']);
  if (avgPrice != null) {
    fTradeKLineItem.avgPrice = avgPrice;
  }
  return fTradeKLineItem;
}

Map<String, dynamic> $FTradeKLineItemToJson(FTradeKLineItem entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['time'] = entity.time;
  data['price'] = entity.price;
  data['volume'] = entity.volume;
  data['open'] = entity.open;
  data['close'] = entity.close;
  data['high'] = entity.high;
  data['low'] = entity.low;
  data['amount'] = entity.amount;
  data['avgPrice'] = entity.avgPrice;
  return data;
}

extension FTradeKLineItemExtension on FTradeKLineItem {
  FTradeKLineItem copyWith({
    int? time,
    double? price,
    double? volume,
    double? open,
    double? close,
    double? high,
    double? low,
    double? amount,
    double? avgPrice,
  }) {
    return FTradeKLineItem()
      ..time = time ?? this.time
      ..price = price ?? this.price
      ..volume = volume ?? this.volume
      ..open = open ?? this.open
      ..close = close ?? this.close
      ..high = high ?? this.high
      ..low = low ?? this.low
      ..amount = amount ?? this.amount
      ..avgPrice = avgPrice ?? this.avgPrice;
  }
}