import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/contract/domain/models/interest_coupon/interest_coupon_model.dart';
import 'package:easy_localization/easy_localization.dart';


InterestCouponModel $InterestCouponModelFromJson(Map<String, dynamic> json) {
  final InterestCouponModel interestCouponModel = InterestCouponModel();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    interestCouponModel.id = id;
  }
  final double? amount = jsonConvert.convert<double>(json['amount']);
  if (amount != null) {
    interestCouponModel.amount = amount;
  }
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    interestCouponModel.currency = currency;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    interestCouponModel.status = status;
  }
  final double? remainingAmount = jsonConvert.convert<double>(
      json['remainingAmount']);
  if (remainingAmount != null) {
    interestCouponModel.remainingAmount = remainingAmount;
  }
  final double? usedAmount = jsonConvert.convert<double>(json['usedAmount']);
  if (usedAmount != null) {
    interestCouponModel.usedAmount = usedAmount;
  }
  final int? userId = jsonConvert.convert<int>(json['userId']);
  if (userId != null) {
    interestCouponModel.userId = userId;
  }
  final String? validStartDate = jsonConvert.convert<String>(
      json['validStartDate']);
  if (validStartDate != null) {
    interestCouponModel.validStartDate = validStartDate;
  }
  final String? validEndDate = jsonConvert.convert<String>(
      json['validEndDate']);
  if (validEndDate != null) {
    interestCouponModel.validEndDate = validEndDate;
  }
  final int? fromType = jsonConvert.convert<int>(json['fromType']);
  if (fromType != null) {
    interestCouponModel.fromType = fromType;
  }
  return interestCouponModel;
}

Map<String, dynamic> $InterestCouponModelToJson(InterestCouponModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['amount'] = entity.amount;
  data['currency'] = entity.currency;
  data['status'] = entity.status;
  data['remainingAmount'] = entity.remainingAmount;
  data['usedAmount'] = entity.usedAmount;
  data['userId'] = entity.userId;
  data['validStartDate'] = entity.validStartDate;
  data['validEndDate'] = entity.validEndDate;
  data['fromType'] = entity.fromType;
  return data;
}

extension InterestCouponModelExtension on InterestCouponModel {
  InterestCouponModel copyWith({
    int? id,
    double? amount,
    String? currency,
    int? status,
    double? remainingAmount,
    double? usedAmount,
    int? userId,
    String? validStartDate,
    String? validEndDate,
    int? fromType,
  }) {
    return InterestCouponModel()
      ..id = id ?? this.id
      ..amount = amount ?? this.amount
      ..currency = currency ?? this.currency
      ..status = status ?? this.status
      ..remainingAmount = remainingAmount ?? this.remainingAmount
      ..usedAmount = usedAmount ?? this.usedAmount
      ..userId = userId ?? this.userId
      ..validStartDate = validStartDate ?? this.validStartDate
      ..validEndDate = validEndDate ?? this.validEndDate
      ..fromType = fromType ?? this.fromType;
  }
}

InterestCouponListModel $InterestCouponListModelFromJson(
    Map<String, dynamic> json) {
  final InterestCouponListModel interestCouponListModel = InterestCouponListModel();
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    interestCouponListModel.current = current;
  }
  final bool? hasNext = jsonConvert.convert<bool>(json['hasNext']);
  if (hasNext != null) {
    interestCouponListModel.hasNext = hasNext;
  }
  final List<InterestCouponModel>? records = (json['records'] as List<dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<InterestCouponModel>(e) as InterestCouponModel)
      .toList();
  if (records != null) {
    interestCouponListModel.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    interestCouponListModel.total = total;
  }
  return interestCouponListModel;
}

Map<String, dynamic> $InterestCouponListModelToJson(
    InterestCouponListModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['current'] = entity.current;
  data['hasNext'] = entity.hasNext;
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  return data;
}

extension InterestCouponListModelExtension on InterestCouponListModel {
  InterestCouponListModel copyWith({
    int? current,
    bool? hasNext,
    List<InterestCouponModel>? records,
    int? total,
  }) {
    return InterestCouponListModel()
      ..current = current ?? this.current
      ..hasNext = hasNext ?? this.hasNext
      ..records = records ?? this.records
      ..total = total ?? this.total;
  }
}