import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:easy_localization/easy_localization.dart';

import 'package:gp_stock_app/features/account/domain/models/order/order_response.dart';

import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';

import 'package:gp_stock_app/shared/models/instrument/instrument.dart';


FTradeAcctOrderModel $FTradeAcctOrderModelFromJson(Map<String, dynamic> json) {
  return FTradeAcctOrderModel(
      current: jsonConvert.convert<int>(json['current']) ?? 0,
      hasNext: jsonConvert.convert<bool>(json['hasNext']) ?? false,
      records: jsonConvert.convertListNotNull<FTradeAcctOrderRecords>(
          json['records']) ?? <FTradeAcctOrderRecords>[],
      total: jsonConvert.convert<int>(json['total']) ?? 0);
}

Map<String, dynamic> $FTradeAcctOrderModelToJson(FTradeAcctOrderModel entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['current'] = entity.current;
  data['hasNext'] = entity.hasNext;
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  return data;
}

extension FTradeAcctOrderModelExtension on FTradeAcctOrderModel {
  FTradeAcctOrderModel copyWith({
    int? current,
    bool? hasNext,
    List<FTradeAcctOrderRecords>? records,
    int? total,
  }) {
    return FTradeAcctOrderModel(current: current ?? this.current,
        hasNext: hasNext ?? this.hasNext,
        records: records ?? this.records,
        total: total ?? this.total);
  }
}

FTradeAcctOrderRecords $FTradeAcctOrderRecordsFromJson(
    Map<String, dynamic> json) {
  return FTradeAcctOrderRecords(
      buyAvgPrice: jsonConvert.convert<double>(json['buyAvgPrice']) ?? 0.0,
      buyTotalNum: jsonConvert.convert<double>(json['buyTotalNum']) ?? 0.0,
      positionTotalNum: jsonConvert.convert<double>(json['positionTotalNum']) ??
          0.0,
      costPrice: jsonConvert.convert<double>(json['costPrice']) ?? 0.0,
      createTime: jsonConvert.convert<String>(json['createTime']) ?? '',
      currency: jsonConvert.convert<String>(json['currency']) ?? '',
      direction: jsonConvert.convert<int>(json['direction']) ?? 0,
      tradeType: jsonConvert.convert<int>(json['tradeType']) ?? 0,
      disableNum: jsonConvert.convert<double>(json['disableNum']) ?? 0.0,
      feeAmount: jsonConvert.convert<double>(json['feeAmount']) ?? 0.0,
      marginAmount: jsonConvert.convert<double>(json['marginAmount']) ?? 0.0,
      marginRatio: jsonConvert.convert<double>(json['marginRatio']) ?? 0.0,
      floatingProfitLoss: jsonConvert.convert<double>(
          json['floatingProfitLoss']) ?? 0.0,
      floatingProfitLossRate: jsonConvert.convert<double>(
          json['floatingProfitLossRate']) ?? 0.0,
      id: jsonConvert.convert<int>(json['id']) ?? 0,
      market: jsonConvert.convert<String>(json['market']) ?? '',
      marketValue: jsonConvert.convert<double>(json['marketValue']) ?? 0.0,
      restNum: jsonConvert.convert<double>(json['restNum']) ?? 0.0,
      availableMargin: jsonConvert.convert<double>(json['availableMargin']) ??
          0.0,
      securityType: jsonConvert.convert<String>(json['securityType']) ?? '',
      stockPrice: jsonConvert.convert<double>(json['stockPrice']) ?? 0.0,
      symbol: jsonConvert.convert<String>(json['symbol']) ?? '',
      symbolName: jsonConvert.convert<String>(json['symbolName']) ?? '',
      tradeUnit: jsonConvert.convert<double>(json['tradeUnit']) ?? 0.0,
      type: jsonConvert.convert<int>(json['type']) ?? 0,
      status: jsonConvert.convert<int>(json['status']) ?? 0,
      tradePrice: jsonConvert.convert<double>(json['tradePrice']) ?? 0.0,
      dealNum: jsonConvert.convert<double>(json['dealNum']) ?? 0.0,
      tradeNum: jsonConvert.convert<double>(json['tradeNum']) ?? 0.0,
      cancelTime: jsonConvert.convert<String>(json['cancelTime']) ?? '',
      dealPrice: jsonConvert.convert<double>(json['dealPrice']) ?? 0.0,
      dealTime: jsonConvert.convert<String>(json['dealTime']) ?? '',
      priceType: jsonConvert.convert<int>(json['priceType']) ?? 0,
      tradeTime: jsonConvert.convert<String>(json['tradeTime']) ?? '',
      winAmount: jsonConvert.convert<double>(json['winAmount']) ?? 0.0,
      tradeFee: jsonConvert.convert<double>(json['tradeFee']) ?? 0.0,
      contractId: jsonConvert.convert<int>(json['contractId']) ?? 0,
      contractType: jsonConvert.convert<int>(json['contractType']) ?? 0,
      periodType: jsonConvert.convert<int>(json['periodType']) ?? 0,
      multiple: jsonConvert.convert<int>(json['multiple']) ?? 0,
      contractAccountId: jsonConvert.convert<int>(json['contractAccountId']) ??
          0,
      transactionAmount: jsonConvert.convert<double>(
          json['transactionAmount']) ?? 0.0,
      productCode: jsonConvert.convert<String>(json['productCode']) ?? '',
      takeProfitValue: jsonConvert.convert<double>(json['takeProfitValue']) ??
          0.0,
      stopLossValue: jsonConvert.convert<double>(json['stopLossValue']) ?? 0.0);
}

Map<String, dynamic> $FTradeAcctOrderRecordsToJson(
    FTradeAcctOrderRecords entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['buyAvgPrice'] = entity.buyAvgPrice;
  data['buyTotalNum'] = entity.buyTotalNum;
  data['positionTotalNum'] = entity.positionTotalNum;
  data['costPrice'] = entity.costPrice;
  data['createTime'] = entity.createTime;
  data['currency'] = entity.currency;
  data['direction'] = entity.direction;
  data['tradeType'] = entity.tradeType;
  data['disableNum'] = entity.disableNum;
  data['feeAmount'] = entity.feeAmount;
  data['marginAmount'] = entity.marginAmount;
  data['marginRatio'] = entity.marginRatio;
  data['floatingProfitLoss'] = entity.floatingProfitLoss;
  data['floatingProfitLossRate'] = entity.floatingProfitLossRate;
  data['id'] = entity.id;
  data['market'] = entity.market;
  data['marketValue'] = entity.marketValue;
  data['restNum'] = entity.restNum;
  data['availableMargin'] = entity.availableMargin;
  data['securityType'] = entity.securityType;
  data['stockPrice'] = entity.stockPrice;
  data['symbol'] = entity.symbol;
  data['symbolName'] = entity.symbolName;
  data['tradeUnit'] = entity.tradeUnit;
  data['type'] = entity.type;
  data['status'] = entity.status;
  data['tradePrice'] = entity.tradePrice;
  data['dealNum'] = entity.dealNum;
  data['tradeNum'] = entity.tradeNum;
  data['cancelTime'] = entity.cancelTime;
  data['dealPrice'] = entity.dealPrice;
  data['dealTime'] = entity.dealTime;
  data['priceType'] = entity.priceType;
  data['tradeTime'] = entity.tradeTime;
  data['winAmount'] = entity.winAmount;
  data['tradeFee'] = entity.tradeFee;
  data['contractId'] = entity.contractId;
  data['contractType'] = entity.contractType;
  data['periodType'] = entity.periodType;
  data['multiple'] = entity.multiple;
  data['contractAccountId'] = entity.contractAccountId;
  data['transactionAmount'] = entity.transactionAmount;
  data['productCode'] = entity.productCode;
  data['takeProfitValue'] = entity.takeProfitValue;
  data['stopLossValue'] = entity.stopLossValue;
  return data;
}

extension FTradeAcctOrderRecordsExtension on FTradeAcctOrderRecords {
  FTradeAcctOrderRecords copyWith({
    double? buyAvgPrice,
    double? buyTotalNum,
    double? positionTotalNum,
    double? costPrice,
    String? createTime,
    String? currency,
    int? direction,
    int? tradeType,
    double? disableNum,
    double? feeAmount,
    double? marginAmount,
    double? marginRatio,
    double? floatingProfitLoss,
    double? floatingProfitLossRate,
    int? id,
    String? market,
    double? marketValue,
    double? restNum,
    double? availableMargin,
    String? securityType,
    double? stockPrice,
    String? symbol,
    String? symbolName,
    double? tradeUnit,
    int? type,
    int? status,
    double? tradePrice,
    double? dealNum,
    double? tradeNum,
    String? cancelTime,
    double? dealPrice,
    String? dealTime,
    int? priceType,
    String? tradeTime,
    double? winAmount,
    double? tradeFee,
    int? contractId,
    int? contractType,
    int? periodType,
    int? multiple,
    int? contractAccountId,
    double? transactionAmount,
    String? productCode,
    double? takeProfitValue,
    double? stopLossValue,
  }) {
    return FTradeAcctOrderRecords(buyAvgPrice: buyAvgPrice ?? this.buyAvgPrice,
        buyTotalNum: buyTotalNum ?? this.buyTotalNum,
        positionTotalNum: positionTotalNum ?? this.positionTotalNum,
        costPrice: costPrice ?? this.costPrice,
        createTime: createTime ?? this.createTime,
        currency: currency ?? this.currency,
        direction: direction ?? this.direction,
        tradeType: tradeType ?? this.tradeType,
        disableNum: disableNum ?? this.disableNum,
        feeAmount: feeAmount ?? this.feeAmount,
        marginAmount: marginAmount ?? this.marginAmount,
        marginRatio: marginRatio ?? this.marginRatio,
        floatingProfitLoss: floatingProfitLoss ?? this.floatingProfitLoss,
        floatingProfitLossRate: floatingProfitLossRate ??
            this.floatingProfitLossRate,
        id: id ?? this.id,
        market: market ?? this.market,
        marketValue: marketValue ?? this.marketValue,
        restNum: restNum ?? this.restNum,
        availableMargin: availableMargin ?? this.availableMargin,
        securityType: securityType ?? this.securityType,
        stockPrice: stockPrice ?? this.stockPrice,
        symbol: symbol ?? this.symbol,
        symbolName: symbolName ?? this.symbolName,
        tradeUnit: tradeUnit ?? this.tradeUnit,
        type: type ?? this.type,
        status: status ?? this.status,
        tradePrice: tradePrice ?? this.tradePrice,
        dealNum: dealNum ?? this.dealNum,
        tradeNum: tradeNum ?? this.tradeNum,
        cancelTime: cancelTime ?? this.cancelTime,
        dealPrice: dealPrice ?? this.dealPrice,
        dealTime: dealTime ?? this.dealTime,
        priceType: priceType ?? this.priceType,
        tradeTime: tradeTime ?? this.tradeTime,
        winAmount: winAmount ?? this.winAmount,
        tradeFee: tradeFee ?? this.tradeFee,
        contractId: contractId ?? this.contractId,
        contractType: contractType ?? this.contractType,
        periodType: periodType ?? this.periodType,
        multiple: multiple ?? this.multiple,
        contractAccountId: contractAccountId ?? this.contractAccountId,
        transactionAmount: transactionAmount ?? this.transactionAmount,
        productCode: productCode ?? this.productCode,
        takeProfitValue: takeProfitValue ?? this.takeProfitValue,
        stopLossValue: stopLossValue ?? this.stopLossValue);
  }
}